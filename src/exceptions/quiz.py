"""
Quiz-related exception classes
"""

from typing import Optional
from .base import XvionException


class QuizError(XvionException):
    """Raised when quiz processing fails"""
    
    def __init__(self, message: str, quiz_id: Optional[str] = None):
        super().__init__(message, error_code="QUIZ_ERROR")
        self.quiz_id = quiz_id
        
        if quiz_id:
            self.details['quiz_id'] = quiz_id


class QuestionValidationError(XvionException):
    """Raised when question validation fails"""
    
    def __init__(self, message: str, question_id: Optional[str] = None, field: Optional[str] = None):
        super().__init__(message, error_code="QUESTION_VALIDATION_ERROR")
        self.question_id = question_id
        self.field = field
        
        if question_id:
            self.details['question_id'] = question_id
        if field:
            self.details['field'] = field


class QuizDataError(XvionException):
    """Raised when quiz data is invalid or corrupted"""
    
    def __init__(self, message: str, data_source: Optional[str] = None, line_number: Optional[int] = None):
        super().__init__(message, error_code="QUIZ_DATA_ERROR")
        self.data_source = data_source
        self.line_number = line_number
        
        if data_source:
            self.details['data_source'] = data_source
        if line_number:
            self.details['line_number'] = line_number


class CSVParsingError(XvionException):
    """Raised when CSV parsing fails"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, row_number: Optional[int] = None, column: Optional[str] = None):
        super().__init__(message, error_code="CSV_PARSING_ERROR")
        self.file_path = file_path
        self.row_number = row_number
        self.column = column
        
        if file_path:
            self.details['file_path'] = file_path
        if row_number:
            self.details['row_number'] = row_number
        if column:
            self.details['column'] = column


class InsufficientQuestionsError(XvionException):
    """Raised when there are not enough questions for video generation"""
    
    def __init__(self, message: str, available_questions: Optional[int] = None, required_questions: Optional[int] = None):
        super().__init__(message, error_code="INSUFFICIENT_QUESTIONS_ERROR")
        self.available_questions = available_questions
        self.required_questions = required_questions
        
        if available_questions is not None:
            self.details['available_questions'] = available_questions
        if required_questions is not None:
            self.details['required_questions'] = required_questions
