"""
Media-related exception classes
"""

from typing import Optional, Dict, Any
from .base import XvionException


class MediaDownloadError(XvionException):
    """Raised when media download fails"""
    
    def __init__(self, message: str, url: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message, error_code="MEDIA_DOWNLOAD_ERROR")
        self.url = url
        self.status_code = status_code
        
        if url:
            self.details['url'] = url
        if status_code:
            self.details['status_code'] = status_code


class PhotoAPIError(XvionException):
    """Raised when photo <PERSON> fails"""
    
    def __init__(self, message: str, api_name: Optional[str] = None, search_term: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message, error_code="PHOTO_API_ERROR")
        self.api_name = api_name
        self.search_term = search_term
        self.status_code = status_code
        
        if api_name:
            self.details['api_name'] = api_name
        if search_term:
            self.details['search_term'] = search_term
        if status_code:
            self.details['status_code'] = status_code


class EmojiError(XvionException):
    """Raised when emoji processing fails"""
    
    def __init__(self, message: str, emoji_code: Optional[str] = None, search_term: Optional[str] = None):
        super().__init__(message, error_code="EMOJI_ERROR")
        self.emoji_code = emoji_code
        self.search_term = search_term
        
        if emoji_code:
            self.details['emoji_code'] = emoji_code
        if search_term:
            self.details['search_term'] = search_term


class AudioGenerationError(XvionException):
    """Raised when audio generation fails"""
    
    def __init__(self, message: str, text: Optional[str] = None, voice_id: Optional[str] = None, language: Optional[str] = None):
        super().__init__(message, error_code="AUDIO_GENERATION_ERROR")
        self.text = text
        self.voice_id = voice_id
        self.language = language
        
        if text:
            self.details['text'] = text[:100] + "..." if len(text) > 100 else text
        if voice_id:
            self.details['voice_id'] = voice_id
        if language:
            self.details['language'] = language


class ImageProcessingError(XvionException):
    """Raised when image processing fails"""
    
    def __init__(self, message: str, image_path: Optional[str] = None, operation: Optional[str] = None):
        super().__init__(message, error_code="IMAGE_PROCESSING_ERROR")
        self.image_path = image_path
        self.operation = operation
        
        if image_path:
            self.details['image_path'] = image_path
        if operation:
            self.details['operation'] = operation


class FontError(XvionException):
    """Raised when font loading or processing fails"""
    
    def __init__(self, message: str, font_path: Optional[str] = None, font_size: Optional[int] = None):
        super().__init__(message, error_code="FONT_ERROR")
        self.font_path = font_path
        self.font_size = font_size
        
        if font_path:
            self.details['font_path'] = font_path
        if font_size:
            self.details['font_size'] = font_size


class MediaFormatError(XvionException):
    """Raised when media format is not supported"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, format: Optional[str] = None, supported_formats: Optional[list] = None):
        super().__init__(message, error_code="MEDIA_FORMAT_ERROR")
        self.file_path = file_path
        self.format = format
        self.supported_formats = supported_formats
        
        if file_path:
            self.details['file_path'] = file_path
        if format:
            self.details['format'] = format
        if supported_formats:
            self.details['supported_formats'] = supported_formats
