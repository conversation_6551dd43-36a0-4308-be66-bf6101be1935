"""
Base exception classes
"""

from typing import Optional, Dict, Any


class XvionException(Exception):
    """Base exception class for all Xvion-related errors"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, details: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code
        self.details = details or {}
    
    def __str__(self) -> str:
        if self.error_code:
            return f"[{self.error_code}] {self.message}"
        return self.message
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert exception to dictionary for API responses"""
        return {
            'error': self.__class__.__name__,
            'message': self.message,
            'error_code': self.error_code,
            'details': self.details
        }


class ValidationError(XvionException):
    """Raised when data validation fails"""
    
    def __init__(self, message: str, field: Optional[str] = None, value: Optional[Any] = None):
        super().__init__(message, error_code="VALIDATION_ERROR")
        self.field = field
        self.value = value
        
        if field:
            self.details['field'] = field
        if value is not None:
            self.details['value'] = value


class ConfigurationError(XvionException):
    """Raised when configuration is invalid or missing"""
    
    def __init__(self, message: str, config_key: Optional[str] = None):
        super().__init__(message, error_code="CONFIGURATION_ERROR")
        self.config_key = config_key
        
        if config_key:
            self.details['config_key'] = config_key


class ResourceNotFoundError(XvionException):
    """Raised when a required resource is not found"""
    
    def __init__(self, message: str, resource_type: Optional[str] = None, resource_id: Optional[str] = None):
        super().__init__(message, error_code="RESOURCE_NOT_FOUND")
        self.resource_type = resource_type
        self.resource_id = resource_id
        
        if resource_type:
            self.details['resource_type'] = resource_type
        if resource_id:
            self.details['resource_id'] = resource_id


class PermissionError(XvionException):
    """Raised when user lacks required permissions"""
    
    def __init__(self, message: str, required_permission: Optional[str] = None):
        super().__init__(message, error_code="PERMISSION_DENIED")
        self.required_permission = required_permission
        
        if required_permission:
            self.details['required_permission'] = required_permission


class RateLimitError(XvionException):
    """Raised when rate limit is exceeded"""
    
    def __init__(self, message: str, retry_after: Optional[int] = None):
        super().__init__(message, error_code="RATE_LIMIT_EXCEEDED")
        self.retry_after = retry_after
        
        if retry_after:
            self.details['retry_after'] = retry_after


class ExternalServiceError(XvionException):
    """Raised when external service fails"""
    
    def __init__(self, message: str, service_name: Optional[str] = None, status_code: Optional[int] = None):
        super().__init__(message, error_code="EXTERNAL_SERVICE_ERROR")
        self.service_name = service_name
        self.status_code = status_code
        
        if service_name:
            self.details['service_name'] = service_name
        if status_code:
            self.details['status_code'] = status_code
