"""
Custom exceptions for Xvion Video Generator

This module contains all custom exception classes used throughout the application.
"""

from .base import XvionException, ValidationError, ConfigurationError
from .video import VideoGenerationError, VideoProcessingError, FFmpegError
from .media import MediaDownloadError, PhotoAPIError, EmojiError, AudioGenerationError
from .translation import TranslationError, LanguageDetectionError
from .quiz import QuizError, QuestionValidationError

__all__ = [
    'XvionException',
    'ValidationError',
    'ConfigurationError',
    'VideoGenerationError',
    'VideoProcessingError',
    'FFmpegError',
    'MediaDownloadError',
    'PhotoAPIError',
    'EmojiError',
    'AudioGenerationError',
    'TranslationError',
    'LanguageDetectionError',
    'QuizError',
    'QuestionValidationError'
]
