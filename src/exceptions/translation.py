"""
Translation-related exception classes
"""

from typing import Optional
from .base import XvionException


class TranslationError(XvionException):
    """Raised when translation fails"""
    
    def __init__(self, message: str, text: Optional[str] = None, source_language: Optional[str] = None, target_language: Optional[str] = None):
        super().__init__(message, error_code="TRANSLATION_ERROR")
        self.text = text
        self.source_language = source_language
        self.target_language = target_language
        
        if text:
            # Truncate long text for details
            self.details['text'] = text[:100] + "..." if len(text) > 100 else text
        if source_language:
            self.details['source_language'] = source_language
        if target_language:
            self.details['target_language'] = target_language


class LanguageDetectionError(XvionException):
    """Raised when language detection fails"""
    
    def __init__(self, message: str, text: Optional[str] = None):
        super().__init__(message, error_code="LANGUAGE_DETECTION_ERROR")
        self.text = text
        
        if text:
            # Truncate long text for details
            self.details['text'] = text[:100] + "..." if len(text) > 100 else text


class TranslationAPIError(XvionException):
    """Raised when translation API fails"""
    
    def __init__(self, message: str, api_name: Optional[str] = None, status_code: Optional[int] = None, api_response: Optional[str] = None):
        super().__init__(message, error_code="TRANSLATION_API_ERROR")
        self.api_name = api_name
        self.status_code = status_code
        self.api_response = api_response
        
        if api_name:
            self.details['api_name'] = api_name
        if status_code:
            self.details['status_code'] = status_code
        if api_response:
            # Truncate long API response for details
            self.details['api_response'] = api_response[:200] + "..." if len(api_response) > 200 else api_response


class UnsupportedLanguageError(XvionException):
    """Raised when language is not supported"""
    
    def __init__(self, message: str, language: Optional[str] = None, supported_languages: Optional[list] = None):
        super().__init__(message, error_code="UNSUPPORTED_LANGUAGE_ERROR")
        self.language = language
        self.supported_languages = supported_languages
        
        if language:
            self.details['language'] = language
        if supported_languages:
            self.details['supported_languages'] = supported_languages
