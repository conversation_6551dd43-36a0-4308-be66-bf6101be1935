"""
Video-related exception classes
"""

from typing import Optional, Dict, Any
from .base import XvionException


class VideoGenerationError(XvionException):
    """Raised when video generation fails"""
    
    def __init__(self, message: str, job_id: Optional[str] = None, stage: Optional[str] = None):
        super().__init__(message, error_code="VIDEO_GENERATION_ERROR")
        self.job_id = job_id
        self.stage = stage
        
        if job_id:
            self.details['job_id'] = job_id
        if stage:
            self.details['stage'] = stage


class VideoProcessingError(XvionException):
    """Raised when video processing fails"""
    
    def __init__(self, message: str, input_file: Optional[str] = None, output_file: Optional[str] = None):
        super().__init__(message, error_code="VIDEO_PROCESSING_ERROR")
        self.input_file = input_file
        self.output_file = output_file
        
        if input_file:
            self.details['input_file'] = input_file
        if output_file:
            self.details['output_file'] = output_file


class FFmpegError(XvionException):
    """Raised when FFmpeg command fails"""
    
    def __init__(self, message: str, command: Optional[list] = None, return_code: Optional[int] = None, stderr: Optional[str] = None):
        super().__init__(message, error_code="FFMPEG_ERROR")
        self.command = command
        self.return_code = return_code
        self.stderr = stderr
        
        if command:
            self.details['command'] = ' '.join(command) if isinstance(command, list) else str(command)
        if return_code is not None:
            self.details['return_code'] = return_code
        if stderr:
            self.details['stderr'] = stderr


class VideoFormatError(XvionException):
    """Raised when video format is not supported"""
    
    def __init__(self, message: str, format: Optional[str] = None, supported_formats: Optional[list] = None):
        super().__init__(message, error_code="VIDEO_FORMAT_ERROR")
        self.format = format
        self.supported_formats = supported_formats
        
        if format:
            self.details['format'] = format
        if supported_formats:
            self.details['supported_formats'] = supported_formats


class VideoFileError(XvionException):
    """Raised when video file operations fail"""
    
    def __init__(self, message: str, file_path: Optional[str] = None, operation: Optional[str] = None):
        super().__init__(message, error_code="VIDEO_FILE_ERROR")
        self.file_path = file_path
        self.operation = operation
        
        if file_path:
            self.details['file_path'] = file_path
        if operation:
            self.details['operation'] = operation


class VideoCompositionError(XvionException):
    """Raised when video composition fails"""
    
    def __init__(self, message: str, layer: Optional[str] = None, timestamp: Optional[float] = None):
        super().__init__(message, error_code="VIDEO_COMPOSITION_ERROR")
        self.layer = layer
        self.timestamp = timestamp
        
        if layer:
            self.details['layer'] = layer
        if timestamp is not None:
            self.details['timestamp'] = timestamp


class VideoRenderError(XvionException):
    """Raised when video rendering fails"""
    
    def __init__(self, message: str, progress: Optional[int] = None, estimated_time_remaining: Optional[float] = None):
        super().__init__(message, error_code="VIDEO_RENDER_ERROR")
        self.progress = progress
        self.estimated_time_remaining = estimated_time_remaining
        
        if progress is not None:
            self.details['progress'] = progress
        if estimated_time_remaining is not None:
            self.details['estimated_time_remaining'] = estimated_time_remaining
