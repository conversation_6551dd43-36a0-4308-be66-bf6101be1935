"""
Main entry point for the new OOP Xvion Video Generator

This demonstrates how to use the new clean architecture.
"""

import asyncio
import sys
import os
from typing import List

# Add src to path for imports
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..'))

from src.models.quiz import Quiz, Question
from src.models.video import VideoGenerationConfig
from src.models.account import AccountConfig
from src.services.video_generation_service import VideoGenerationService
from src.config.settings import settings
from src.exceptions.base import XvionException


async def main():
    """Main function demonstrating the new OOP structure"""
    
    print("🚀 Starting Xvion Video Generator V2.0.0 (OOP)")
    print("=" * 60)
    
    try:
        # Initialize the video generation service
        video_service = VideoGenerationService()
        
        # Create a sample quiz
        quiz = create_sample_quiz()
        print(f"📝 Created sample quiz with {quiz.question_count} questions")
        
        # Create account configuration
        account_config = AccountConfig(
            name="TestAccount",
            display_name="@TEST_ACCOUNT",
            color_mode="mode1",
            background_folder="./assets/Back Videos",
            output_folder="./output",
            font_path="./assets/Fonts/NotoSansArabic-Bold.ttf"
        )
        print(f"👤 Created account config: {account_config.display_name}")
        
        # Create video generation configuration
        video_config = VideoGenerationConfig(
            account_name=account_config.name,
            display_name=account_config.display_name,
            color_mode=account_config.color_mode,
            num_questions=3,  # Use fewer questions for demo
            enable_photos=True,
            enable_emojis=True,
            enable_audio=True
        )
        print(f"⚙️ Created video config for {video_config.num_questions} questions")
        
        # Generate output path
        output_filename = f"demo_video_{video_service.file_utils.generate_random_filename('', '.mp4')[1:]}"
        output_path = settings.get_output_file_path(output_filename)
        print(f"📁 Output path: {output_path}")
        
        # Estimate generation time
        estimated_time = await video_service.estimate_generation_time(
            video_config.num_questions, video_config
        )
        print(f"⏱️ Estimated generation time: {estimated_time:.1f} seconds")
        
        # Validate configuration
        if not video_service.validate_config(video_config):
            print("❌ Invalid video configuration")
            return
        
        print("✅ Configuration validated successfully")
        
        # Check API connections
        print("\n🔧 Checking API connections...")
        
        # Check translation service
        if settings.api_keys.has_translation_api:
            translation_valid = await video_service.translation_service.validate_connection()
            print(f"🌐 Translation API: {'✅ Connected' if translation_valid else '❌ Failed'}")
        else:
            print("🌐 Translation API: ⚠️ Not configured")
        
        # Check audio service
        if settings.api_keys.has_tts_api:
            audio_valid = await video_service.audio_service.validate_connection()
            print(f"🔊 Audio API: {'✅ Connected' if audio_valid else '❌ Failed'}")
        else:
            print("🔊 Audio API: ⚠️ Not configured")
        
        # Check photo APIs
        if settings.api_keys.has_photo_apis:
            available_apis = settings.api_keys.available_photo_apis
            print(f"📸 Photo APIs: ✅ Available ({', '.join(available_apis)})")
        else:
            print("📸 Photo APIs: ⚠️ Not configured")
        
        print("\n🎬 Starting video generation...")
        print("-" * 40)
        
        # Generate video (this would normally take much longer)
        try:
            result_path = await video_service.generate_video_from_quiz(
                quiz=quiz,
                config=video_config,
                account_config=account_config,
                output_path=output_path,
                num_questions=video_config.num_questions
            )
            
            print(f"✅ Video generated successfully!")
            print(f"📁 Output file: {result_path}")
            
            # Check if file exists and get size
            if os.path.exists(result_path):
                file_size = os.path.getsize(result_path)
                print(f"📊 File size: {file_size / (1024*1024):.2f} MB")
            
        except Exception as e:
            print(f"❌ Video generation failed: {str(e)}")
            if isinstance(e, XvionException):
                print(f"   Error details: {e.to_dict()}")
    
    except Exception as e:
        print(f"❌ Application error: {str(e)}")
        if isinstance(e, XvionException):
            print(f"   Error details: {e.to_dict()}")
    
    print("\n" + "=" * 60)
    print("🏁 Xvion Video Generator completed")


def create_sample_quiz() -> Quiz:
    """Create a sample quiz for demonstration"""
    
    # Create sample questions
    questions = [
        Question(
            id="q1",
            text="ما هو أكبر كوكب في النظام الشمسي؟",
            options=[
                "أ) المشتري",
                "ب) زحل", 
                "ج) الأرض"
            ],
            correct_answer="Answer A",
            explanation="المشتري هو أكبر كوكب في النظام الشمسي"
        ),
        Question(
            id="q2",
            text="كم عدد القارات في العالم؟",
            options=[
                "أ) خمس قارات",
                "ب) ست قارات",
                "ج) سبع قارات"
            ],
            correct_answer="Answer C",
            explanation="يوجد سبع قارات في العالم"
        ),
        Question(
            id="q3",
            text="ما هي عاصمة فرنسا؟",
            options=[
                "أ) لندن",
                "ب) باريس",
                "ج) روما"
            ],
            correct_answer="Answer B",
            explanation="باريس هي عاصمة فرنسا"
        ),
        Question(
            id="q4",
            text="كم عدد أيام السنة؟",
            options=[
                "أ) 364 يوم",
                "ب) 365 يوم",
                "ج) 366 يوم"
            ],
            correct_answer="Answer B",
            explanation="السنة العادية تحتوي على 365 يوم"
        ),
        Question(
            id="q5",
            text="ما هو أسرع حيوان في العالم؟",
            options=[
                "أ) الفهد",
                "ب) الأسد",
                "ج) الحصان"
            ],
            correct_answer="Answer A",
            explanation="الفهد هو أسرع حيوان بري في العالم"
        ),
        Question(
            id="q6",
            text="كم عدد ألوان قوس قزح؟",
            options=[
                "أ) ستة ألوان",
                "ب) سبعة ألوان",
                "ج) ثمانية ألوان"
            ],
            correct_answer="Answer B",
            explanation="قوس قزح يحتوي على سبعة ألوان"
        )
    ]
    
    # Create quiz
    quiz = Quiz(
        id="demo-quiz",
        title="اختبار المعرفة العامة",
        description="اختبار تجريبي للمعرفة العامة باللغة العربية",
        language="ar"
    )
    
    # Add questions to quiz
    for question in questions:
        quiz.add_question(question)
    
    return quiz


def print_project_structure():
    """Print the new project structure"""
    
    print("\n📁 New Project Structure:")
    print("=" * 40)
    
    structure = """
src/
├── models/                 # Domain models
│   ├── quiz.py            # Quiz and Question models
│   ├── video.py           # Video generation models
│   ├── account.py         # Account models
│   ├── media.py           # Media models
│   └── positioning.py     # Layout configuration models
├── services/              # Business logic services
│   ├── video_generation_service.py
│   ├── translation_service.py
│   ├── audio_service.py
│   ├── photo_service.py
│   └── emoji_service.py
├── config/                # Configuration management
│   ├── base.py           # Base configuration class
│   ├── video.py          # Video settings
│   ├── positioning.py    # Layout settings
│   ├── api_keys.py       # API keys management
│   ├── accounts.py       # Account configurations
│   └── settings.py       # Main settings
├── exceptions/            # Custom exceptions
│   ├── base.py           # Base exceptions
│   ├── video.py          # Video-related exceptions
│   ├── media.py          # Media-related exceptions
│   ├── translation.py    # Translation exceptions
│   └── quiz.py           # Quiz-related exceptions
├── interfaces/            # Abstract interfaces
│   ├── video_generator.py
│   ├── translation_service.py
│   └── media_service.py
├── utils/                 # Utility classes
│   ├── file_utils.py     # File operations
│   ├── video_utils.py    # Video processing
│   ├── image_utils.py    # Image processing
│   ├── text_utils.py     # Text processing
│   └── csv_utils.py      # CSV handling
└── main.py               # Main entry point
"""
    
    print(structure)


if __name__ == "__main__":
    print_project_structure()
    
    # Run the main demo
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️ Process interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)
