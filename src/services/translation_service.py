"""
Translation service implementation using DeepL
"""

import asyncio
from typing import Optional, List, Dict, Any

import deepl
from langdetect import detect, DetectorFactory

from ..interfaces.translation_service import ITranslationService
from ..exceptions.translation import TranslationError, LanguageDetectionError, TranslationAPIError, UnsupportedLanguageError
from ..config.settings import settings


# Make language detection deterministic
DetectorFactory.seed = 0


class DeepLTranslationService(ITranslationService):
    """DeepL translation service implementation"""
    
    def __init__(self):
        self.api_key = settings.api_keys.deepl_auth_key
        self.translator = None
        self._supported_languages = None
        
        if self.api_key:
            try:
                self.translator = deepl.Translator(self.api_key)
            except Exception as e:
                print(f"Warning: Failed to initialize DeepL translator: {e}")
    
    async def translate_text(
        self,
        text: str,
        target_language: str = "en",
        source_language: Optional[str] = None
    ) -> str:
        """Translate text to target language"""
        
        if not self.translator:
            raise TranslationAPIError("DeepL translator not initialized", api_name="deepl")
        
        # Clean the text first
        clean_text = text.strip()
        if not clean_text:
            return text
        
        try:
            # Use provided source language or detect it
            if source_language:
                detected_lang = source_language
                print(f"🔍 Using provided source language '{detected_lang}' for: '{clean_text}'")
            else:
                detected_lang = await self.detect_language(clean_text)
                print(f"🔍 Language detection for '{clean_text}': {detected_lang}")
            
            # Skip translation if already in target language
            if detected_lang.lower() == target_language.lower():
                print(f"✅ Text '{clean_text}' already in target language, skipping translation")
                return text
            
            # Convert target language to DeepL format
            deepl_target = self._convert_to_deepl_language(target_language)
            
            print(f"🌐 Attempting translation for '{clean_text}' (source: {detected_lang} -> target: {deepl_target})")
            
            # Perform translation
            result = await asyncio.to_thread(
                self.translator.translate_text,
                clean_text,
                target_lang=deepl_target
            )
            
            translated_text = result.text.strip()
            print(f"✅ Translation successful: '{clean_text}' → '{translated_text}'")
            return translated_text
            
        except deepl.exceptions.DeepLException as e:
            print(f"❌ DeepL API Error for '{text}': {e}")
            
            # Try fallback translation
            fallback_result = self._try_fallback_translation(text, target_language)
            if fallback_result:
                return fallback_result
            
            raise TranslationAPIError(
                f"DeepL translation failed: {str(e)}",
                api_name="deepl",
                api_response=str(e)
            )
        except Exception as e:
            print(f"❌ Translation Error for '{text}': {e}")
            
            # Try fallback translation
            fallback_result = self._try_fallback_translation(text, target_language)
            if fallback_result:
                return fallback_result
            
            raise TranslationError(
                f"Translation failed: {str(e)}",
                text=text,
                source_language=source_language,
                target_language=target_language
            )
    
    async def detect_language(self, text: str) -> str:
        """Detect the language of given text"""
        
        try:
            # Clean the text first
            clean_text = text.strip().replace('\\n', ' ').replace('\n', ' ')
            if not clean_text:
                return 'en'  # Default to English
            
            detected_lang = await asyncio.to_thread(detect, clean_text)
            print(f"🌐 Language detection for '{clean_text[:50]}...' → {detected_lang}")
            return detected_lang
            
        except Exception as e:
            print(f"⚠️ Language detection failed for '{text}': {e}")
            raise LanguageDetectionError(f"Language detection failed: {str(e)}", text=text)
    
    async def translate_batch(
        self,
        texts: List[str],
        target_language: str = "en",
        source_language: Optional[str] = None
    ) -> List[str]:
        """Translate multiple texts in batch"""
        
        if not texts:
            return []
        
        # For now, translate one by one (DeepL batch API could be implemented later)
        results = []
        for text in texts:
            try:
                translated = await self.translate_text(text, target_language, source_language)
                results.append(translated)
            except Exception as e:
                print(f"Warning: Failed to translate '{text}': {e}")
                results.append(text)  # Use original text as fallback
        
        return results
    
    def get_supported_languages(self) -> List[str]:
        """Get list of supported language codes"""
        
        if self._supported_languages is None:
            # DeepL supported languages (as of 2024)
            self._supported_languages = [
                'ar', 'bg', 'cs', 'da', 'de', 'el', 'en', 'es', 'et', 'fi', 'fr',
                'hu', 'id', 'it', 'ja', 'ko', 'lt', 'lv', 'nb', 'nl', 'pl', 'pt',
                'ro', 'ru', 'sk', 'sl', 'sv', 'tr', 'uk', 'zh'
            ]
        
        return self._supported_languages.copy()
    
    def is_language_supported(self, language_code: str) -> bool:
        """Check if language is supported"""
        return language_code.lower() in self.get_supported_languages()
    
    async def get_usage_info(self) -> Dict[str, Any]:
        """Get translation service usage information"""
        
        if not self.translator:
            return {"error": "Translator not initialized"}
        
        try:
            usage = await asyncio.to_thread(self.translator.get_usage)
            return {
                "character_count": usage.character.count,
                "character_limit": usage.character.limit,
                "characters_remaining": usage.character.limit - usage.character.count,
                "usage_percentage": (usage.character.count / usage.character.limit) * 100 if usage.character.limit > 0 else 0
            }
        except Exception as e:
            return {"error": f"Failed to get usage info: {str(e)}"}
    
    def _convert_to_deepl_language(self, language_code: str) -> str:
        """Convert language code to DeepL format"""
        
        # DeepL uses specific codes for some languages
        language_map = {
            'en': 'EN-US',
            'pt': 'PT-PT',
            'zh': 'ZH'
        }
        
        return language_map.get(language_code.lower(), language_code.upper())
    
    def _try_fallback_translation(self, text: str, target_language: str) -> Optional[str]:
        """Try fallback translation using simple word mappings"""
        
        if target_language.lower() != 'en':
            return None  # Only have English fallbacks
        
        # Simple Spanish to English mappings
        spanish_to_english = {
            'pez': 'fish', 'rana': 'frog', 'manzana': 'apple', 'plátano': 'banana',
            'uva': 'grape', 'perro': 'dog', 'gato': 'cat', 'casa': 'house',
            'agua': 'water', 'fuego': 'fire', 'tierra': 'earth', 'aire': 'air',
            'sol': 'sun', 'luna': 'moon', 'estrella': 'star', 'mar': 'sea',
            'montaña': 'mountain', 'árbol': 'tree', 'flor': 'flower', 'pájaro': 'bird',
            'caballo': 'horse', 'vaca': 'cow', 'pollo': 'chicken', 'cerdo': 'pig',
            'pescado': 'fish', 'carne': 'meat', 'verdura': 'vegetable', 'fruta': 'fruit',
            'pan': 'bread', 'leche': 'milk', 'queso': 'cheese', 'huevo': 'egg',
            'azúcar': 'sugar', 'sal': 'salt', 'aceite': 'oil', 'mantequilla': 'butter',
            'arroz': 'rice', 'pasta': 'pasta', 'sopa': 'soup', 'ensalada': 'salad',
            'postre': 'dessert', 'café': 'coffee', 'té': 'tea', 'vino': 'wine',
            'cerveza': 'beer', 'jugo': 'juice', 'refresco': 'soda'
        }
        
        # Try fallback translation
        lower_text = text.lower().strip()
        if lower_text in spanish_to_english:
            fallback_translation = spanish_to_english[lower_text]
            print(f"🔄 Using fallback translation: '{text}' → '{fallback_translation}'")
            return fallback_translation
        
        return None
    
    async def validate_connection(self) -> bool:
        """Validate DeepL API connection"""
        
        if not self.translator:
            return False
        
        try:
            print("🔧 Testing DeepL API connection...")
            
            # Test with a simple Spanish word
            test_result = await asyncio.to_thread(
                self.translator.translate_text,
                "hola",
                target_lang="EN-US"
            )
            print(f"✅ DeepL API test successful: 'hola' → '{test_result.text}'")
            
            # Check usage
            usage = await asyncio.to_thread(self.translator.get_usage)
            print(f"📊 DeepL API usage: {usage.character.count}/{usage.character.limit} characters used")
            
            return True
        except Exception as e:
            print(f"❌ DeepL API test failed: {e}")
            return False
