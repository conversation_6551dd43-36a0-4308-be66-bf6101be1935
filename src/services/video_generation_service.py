"""
Video generation service implementation
"""

import os
import asyncio
import subprocess
from typing import List, Optional, Dict, Any
from datetime import datetime

from ..interfaces.video_generator import IVideoGenerator
from ..models.quiz import Quiz, Question
from ..models.video import VideoJob, VideoGenerationConfig, VideoJobStatus
from ..models.account import AccountConfig
from ..models.media import MediaCollection
from ..exceptions.video import VideoGenerationError, FFmpegError
from ..exceptions.quiz import InsufficientQuestionsError
from ..config.settings import settings
from ..utils.file_utils import FileUtils
from ..utils.video_utils import VideoUtils
from .translation_service import DeepLTranslationService
from .audio_service import ElevenLabsAudioService
from .photo_service import MultiAPIPhotoService
from .emoji_service import EmojiService
from .media_collection_service import MediaCollectionService


class VideoGenerationService(IVideoGenerator):
    """Main video generation service"""
    
    def __init__(self):
        self.translation_service = DeepLTranslationService()
        self.audio_service = ElevenLabsAudioService()
        self.photo_service = MultiAPIPhotoService()
        self.emoji_service = EmojiService()
        self.media_collection_service = MediaCollectionService()
        self.file_utils = FileUtils()
        self.video_utils = VideoUtils()
        
        # Track running jobs
        self.running_jobs: Dict[str, asyncio.Task] = {}
    
    async def generate_video_from_quiz(
        self,
        quiz: Quiz,
        config: VideoGenerationConfig,
        account_config: AccountConfig,
        output_path: str,
        num_questions: int = 6
    ) -> str:
        """Generate video from quiz data"""
        
        # Validate inputs
        if not self.validate_config(config):
            raise VideoGenerationError("Invalid video generation configuration")
        
        if quiz.question_count < num_questions:
            raise InsufficientQuestionsError(
                f"Quiz has {quiz.question_count} questions but {num_questions} are required",
                available_questions=quiz.question_count,
                required_questions=num_questions
            )
        
        try:
            # Get questions for video
            questions = quiz.get_questions_for_video(num_questions)
            
            # Extract data for processing
            question_texts = [q.text for q in questions]
            correct_answers = [q.correct_answer for q in questions]
            answer_options = [q.options for q in questions]
            
            # Generate video
            return await self._generate_video_internal(
                question_texts, correct_answers, answer_options,
                config, account_config, output_path
            )
            
        except Exception as e:
            raise VideoGenerationError(f"Failed to generate video from quiz: {str(e)}")
    
    async def generate_video_from_csv(
        self,
        csv_file_path: str,
        account_config: AccountConfig,
        output_path: str,
        num_questions: int = 6
    ) -> str:
        """Generate video from CSV file"""
        
        try:
            # Load questions from CSV
            from ..utils.csv_utils import CSVUtils
            csv_utils = CSVUtils()
            
            questions, correct_answers, answer_options = csv_utils.load_questions_from_csv(
                csv_file_path, num_questions
            )
            
            # Create default config
            config = VideoGenerationConfig(
                account_name=account_config.name,
                display_name=account_config.display_name,
                color_mode=account_config.color_mode,
                num_questions=num_questions
            )
            
            # Generate video
            return await self._generate_video_internal(
                questions, correct_answers, answer_options,
                config, account_config, output_path
            )
            
        except Exception as e:
            raise VideoGenerationError(f"Failed to generate video from CSV: {str(e)}")
    
    async def process_video_job(self, job: VideoJob) -> VideoJob:
        """Process a video generation job"""
        
        job.start_processing()
        
        try:
            # Create task for this job
            task = asyncio.create_task(self._process_job_internal(job))
            self.running_jobs[job.id] = task
            
            # Wait for completion
            result_path = await task
            
            # Mark job as completed
            job.complete_successfully(result_path)
            
        except asyncio.CancelledError:
            job.cancel()
        except Exception as e:
            job.fail_with_error(str(e))
        finally:
            # Clean up
            if job.id in self.running_jobs:
                del self.running_jobs[job.id]
        
        return job
    
    async def _process_job_internal(self, job: VideoJob) -> str:
        """Internal job processing logic"""
        
        # Update progress
        job.update_progress(10, "Initializing video generation")
        
        # Load quiz data (this would come from repository in real implementation)
        # For now, assume we have the data in job.config
        
        # Create account config from job config
        account_config = AccountConfig(
            name=job.config.account_name,
            display_name=job.config.display_name,
            color_mode=job.config.color_mode,
            background_folder=job.config.background_folder,
            output_folder=job.config.output_folder,
            font_path=job.config.font_path
        )
        
        # Generate output path
        output_filename = self.file_utils.generate_random_filename("quiz_video", ".mp4")
        output_path = os.path.join(job.config.output_folder, output_filename)
        
        # This would be replaced with actual quiz loading logic
        # For now, create dummy data
        questions = ["Sample question?"] * job.config.num_questions
        correct_answers = ["Answer A"] * job.config.num_questions
        answer_options = [["Option A", "Option B", "Option C"]] * job.config.num_questions
        
        # Generate video
        result_path = await self._generate_video_internal(
            questions, correct_answers, answer_options,
            job.config, account_config, output_path, job
        )
        
        return result_path
    
    async def _generate_video_internal(
        self,
        questions: List[str],
        correct_answers: List[str],
        answer_options: List[List[str]],
        config: VideoGenerationConfig,
        account_config: AccountConfig,
        output_path: str,
        job: Optional[VideoJob] = None
    ) -> str:
        """Internal video generation logic"""
        
        try:
            # Step 1: Detect language
            if job:
                job.update_progress(15, "Detecting language")
            
            quiz_language = await self.translation_service.detect_language(questions[0])
            
            # Step 2: Collect media
            if job:
                job.update_progress(25, "Collecting media assets")
            
            media_collection = await self.media_collection_service.collect_media_for_quiz(
                questions, answer_options, quiz_language, config
            )
            
            # Step 3: Generate audio
            if job:
                job.update_progress(40, "Generating audio")
            
            if config.enable_audio:
                audio_files = await self.audio_service.generate_audio_files(
                    questions, correct_answers, quiz_language
                )
                media_collection.audio_files.extend(audio_files.values())
            
            # Step 4: Create text overlays
            if job:
                job.update_progress(55, "Creating text overlays")
            
            text_overlays = await self._create_text_overlays(
                questions, answer_options, correct_answers, config, account_config
            )
            
            # Step 5: Get background video
            if job:
                job.update_progress(70, "Preparing background video")
            
            background_video = self._get_random_background_video(account_config.background_folder)
            
            # Step 6: Compose final video
            if job:
                job.update_progress(85, "Composing final video")
            
            final_video_path = await self._compose_final_video(
                background_video, text_overlays, media_collection, 
                config, output_path
            )
            
            # Step 7: Cleanup
            if job:
                job.update_progress(100, "Finalizing video")
            
            if settings.cleanup_temp_files:
                await self._cleanup_temp_files(text_overlays, media_collection)
            
            return final_video_path
            
        except Exception as e:
            if job:
                job.add_log(f"Error during video generation: {str(e)}")
            raise VideoGenerationError(f"Video generation failed: {str(e)}")
    
    async def _create_text_overlays(
        self,
        questions: List[str],
        answer_options: List[List[str]],
        correct_answers: List[str],
        config: VideoGenerationConfig,
        account_config: AccountConfig
    ) -> List[str]:
        """Create text overlay images"""
        
        from ..core.text.text_box_creator import TextBoxCreator
        
        text_box_creator = TextBoxCreator(settings.positioning, config.color_mode)
        overlay_paths = []
        
        # Create account name text box
        account_overlay = text_box_creator.create_account_name_text_box(
            account_config.display_name, 0
        )
        overlay_paths.append(account_overlay)
        
        # Create question and option text boxes
        for i, (question, options, correct_answer) in enumerate(
            zip(questions, answer_options, correct_answers)
        ):
            # Question text box
            question_overlay = text_box_creator.create_question_text_box(
                question, i, config.color_mode
            )
            overlay_paths.append(question_overlay)
            
            # Option text boxes
            option_overlays = text_box_creator.create_option_text_boxes(
                i, options, correct_answer, config.color_mode, question
            )
            overlay_paths.extend(option_overlays)
        
        return overlay_paths
    
    def _get_random_background_video(self, background_folder: str) -> str:
        """Get random background video from folder"""
        
        try:
            video_files = [
                f for f in os.listdir(background_folder)
                if f.startswith('A (') and f.endswith(').mp4')
            ]
            
            if not video_files:
                raise VideoGenerationError(f"No background videos found in {background_folder}")
            
            import random
            selected_video = random.choice(video_files)
            return os.path.join(background_folder, selected_video)
            
        except Exception as e:
            raise VideoGenerationError(f"Failed to get background video: {str(e)}")
    
    async def _compose_final_video(
        self,
        background_video: str,
        text_overlays: List[str],
        media_collection: MediaCollection,
        config: VideoGenerationConfig,
        output_path: str
    ) -> str:
        """Compose final video with all elements"""
        
        try:
            # Use VideoUtils to compose the video
            return await self.video_utils.compose_video_with_overlays(
                background_video, text_overlays, media_collection,
                config, output_path
            )
            
        except Exception as e:
            raise VideoGenerationError(f"Failed to compose final video: {str(e)}")
    
    async def _cleanup_temp_files(
        self,
        text_overlays: List[str],
        media_collection: MediaCollection
    ) -> None:
        """Clean up temporary files"""
        
        try:
            # Clean up text overlays
            for overlay_path in text_overlays:
                if os.path.exists(overlay_path):
                    os.remove(overlay_path)
            
            # Clean up downloaded media (keep audio files as they might be reused)
            for photo in media_collection.photos:
                if photo.local_path and os.path.exists(photo.local_path):
                    os.remove(photo.local_path)
            
            for emoji in media_collection.emojis:
                if emoji.local_path and os.path.exists(emoji.local_path):
                    os.remove(emoji.local_path)
                    
        except Exception as e:
            # Log cleanup errors but don't fail the video generation
            print(f"Warning: Failed to cleanup temp files: {e}")
    
    def get_supported_formats(self) -> List[str]:
        """Get list of supported video formats"""
        return ['.mp4', '.avi', '.mov', '.mkv']
    
    def validate_config(self, config: VideoGenerationConfig) -> bool:
        """Validate video generation configuration"""
        return config.validate()
    
    async def estimate_generation_time(
        self,
        num_questions: int,
        config: VideoGenerationConfig
    ) -> float:
        """Estimate video generation time in seconds"""
        
        # Base time per question (rough estimate)
        base_time_per_question = 30  # seconds
        
        # Adjust based on features
        multiplier = 1.0
        if config.enable_photos:
            multiplier += 0.3
        if config.enable_emojis:
            multiplier += 0.2
        if config.enable_audio:
            multiplier += 0.5
        
        # Adjust based on quality
        quality_multipliers = {
            'low': 0.7,
            'medium': 1.0,
            'high': 1.5
        }
        multiplier *= quality_multipliers.get(config.video_quality, 1.0)
        
        return num_questions * base_time_per_question * multiplier
    
    async def cancel_job(self, job_id: str) -> bool:
        """Cancel a running video generation job"""
        
        if job_id in self.running_jobs:
            task = self.running_jobs[job_id]
            task.cancel()
            return True
        
        return False
