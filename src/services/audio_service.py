"""
Audio service implementation using ElevenLabs
"""

import os
import asyncio
from typing import List, Dict, Any, Optional
from multiprocessing import Semaphore

from elevenlabs import ElevenLabs, VoiceSettings
import soundfile as sf

from ..interfaces.media_service import IAudioService
from ..models.media import AudioFile
from ..exceptions.media import AudioGenerationError
from ..config.settings import settings
from ..utils.file_utils import FileUtils


class ElevenLabsAudioService(IAudioService):
    """ElevenLabs audio service implementation"""
    
    def __init__(self):
        self.api_key = settings.api_keys.elevenlabs_api_key
        self.client = None
        self.file_utils = FileUtils()
        
        # Semaphore to limit concurrent API requests
        self.semaphore = Semaphore(2)
        
        if self.api_key:
            try:
                self.client = ElevenLabs(api_key=self.api_key)
            except Exception as e:
                print(f"Warning: Failed to initialize ElevenLabs client: {e}")
    
    async def generate_speech(
        self,
        text: str,
        language: str = "ar",
        voice_id: Optional[str] = None
    ) -> AudioFile:
        """Generate speech from text"""
        
        if not self.client:
            raise AudioGenerationError("ElevenLabs client not initialized")
        
        if not text.strip():
            raise AudioGenerationError("Text cannot be empty")
        
        try:
            # Generate unique filename
            filename = self.file_utils.generate_random_filename("audio", ".mp3")
            output_path = settings.get_temp_file_path(filename)
            
            # Use semaphore to limit concurrent requests
            with self.semaphore:
                # Generate audio
                audio_data = await asyncio.to_thread(
                    self._generate_audio_sync,
                    text, voice_id, language
                )
                
                # Save audio file
                with open(output_path, 'wb') as f:
                    f.write(audio_data)
            
            # Get audio duration
            duration = await self.get_audio_duration(output_path)
            
            # Create AudioFile object
            audio_file = AudioFile(
                id=self.file_utils.generate_uuid(),
                text=text,
                language=language,
                voice_id=voice_id
            )
            
            # Get file size
            file_size = os.path.getsize(output_path)
            
            # Mark as generated
            audio_file.mark_generated(output_path, duration, file_size)
            
            print(f"✅ Generated audio for text: '{text[:50]}...' -> {output_path}")
            return audio_file
            
        except Exception as e:
            raise AudioGenerationError(
                f"Failed to generate speech: {str(e)}",
                text=text,
                language=language,
                voice_id=voice_id
            )
    
    def _generate_audio_sync(self, text: str, voice_id: Optional[str], language: str) -> bytes:
        """Synchronous audio generation for thread execution"""
        
        # Default voice settings
        voice_settings = VoiceSettings(
            stability=0.5,
            similarity_boost=0.75,
            style=0.0,
            use_speaker_boost=True
        )
        
        # Use default voice if none specified
        if not voice_id:
            voice_id = self._get_default_voice_for_language(language)
        
        # Generate audio
        audio_generator = self.client.generate(
            text=text,
            voice=voice_id,
            voice_settings=voice_settings,
            model="eleven_multilingual_v2"
        )
        
        # Collect audio data
        audio_data = b"".join(audio_generator)
        return audio_data
    
    async def generate_audio_files(
        self,
        questions: List[str],
        correct_answers: List[str],
        language: str = "ar"
    ) -> Dict[str, AudioFile]:
        """Generate audio files for questions and answers"""
        
        audio_files = {}
        
        try:
            # Generate audio for questions
            for i, question in enumerate(questions):
                try:
                    audio_file = await self.generate_speech(question, language)
                    audio_files[f"question_{i}"] = audio_file
                except Exception as e:
                    print(f"Warning: Failed to generate audio for question {i}: {e}")
            
            # Generate audio for answers
            for i, answer in enumerate(correct_answers):
                try:
                    audio_file = await self.generate_speech(answer, language)
                    audio_files[f"answer_{i}"] = audio_file
                except Exception as e:
                    print(f"Warning: Failed to generate audio for answer {i}: {e}")
            
            return audio_files
            
        except Exception as e:
            raise AudioGenerationError(f"Failed to generate audio files: {str(e)}")
    
    def get_available_voices(self, language: str = "ar") -> List[Dict[str, Any]]:
        """Get available voices for language"""
        
        if not self.client:
            return []
        
        try:
            voices = self.client.voices.get_all()
            
            # Filter voices by language (basic filtering)
            language_voices = []
            for voice in voices.voices:
                # This is a simplified approach - in reality, you'd need to check
                # voice capabilities more thoroughly
                voice_info = {
                    'voice_id': voice.voice_id,
                    'name': voice.name,
                    'category': voice.category,
                    'description': getattr(voice, 'description', ''),
                    'preview_url': getattr(voice, 'preview_url', ''),
                    'available_for_tiers': getattr(voice, 'available_for_tiers', [])
                }
                language_voices.append(voice_info)
            
            return language_voices
            
        except Exception as e:
            print(f"Warning: Failed to get available voices: {e}")
            return []
    
    async def get_audio_duration(self, audio_path: str) -> float:
        """Get duration of audio file in seconds"""
        
        try:
            # Use soundfile to get duration
            info = await asyncio.to_thread(sf.info, audio_path)
            return info.duration
        except Exception as e:
            print(f"Warning: Failed to get audio duration for {audio_path}: {e}")
            return 0.0
    
    async def download_media(self, url: str, local_path: str) -> bool:
        """Download media from URL to local path"""
        # Not applicable for audio generation service
        return False
    
    def validate_url(self, url: str) -> bool:
        """Validate media URL"""
        # Not applicable for audio generation service
        return False
    
    def _get_default_voice_for_language(self, language: str) -> str:
        """Get default voice ID for language"""
        
        # Default voice mappings (these would need to be updated based on available voices)
        default_voices = {
            'ar': 'pNInz6obpgDQGcFmaJgB',  # Adam (example)
            'en': 'EXAVITQu4vr4xnSDxMaL',  # Bella (example)
            'es': 'IKne3meq5aSn9XLyUdCD',  # Charlie (example)
            'fr': 'XrExE9yKIg1WjnnlVkGX',  # Liam (example)
            'de': 'ErXwobaYiN019PkySvjV',  # Antoni (example)
        }
        
        return default_voices.get(language, default_voices['en'])
    
    async def validate_connection(self) -> bool:
        """Validate ElevenLabs API connection"""
        
        if not self.client:
            return False
        
        try:
            print("🔧 Testing ElevenLabs API connection...")
            
            # Test by getting available voices
            voices = await asyncio.to_thread(self.client.voices.get_all)
            print(f"✅ ElevenLabs API test successful: Found {len(voices.voices)} voices")
            
            return True
        except Exception as e:
            print(f"❌ ElevenLabs API test failed: {e}")
            return False
