"""
File utility functions
"""

import os
import shutil
import uuid
import random
import string
from typing import List, Optional
from pathlib import Path


class FileUtils:
    """Utility class for file operations"""
    
    @staticmethod
    def generate_uuid() -> str:
        """Generate a UUID string"""
        return str(uuid.uuid4())
    
    @staticmethod
    def generate_random_filename(prefix: str = "file", extension: str = ".tmp") -> str:
        """Generate a random filename with prefix and extension"""
        random_string = ''.join(random.choices(string.ascii_letters + string.digits, k=8))
        return f"{prefix}_{random_string}{extension}"
    
    @staticmethod
    def ensure_directory_exists(directory_path: str) -> None:
        """Ensure directory exists, create if it doesn't"""
        Path(directory_path).mkdir(parents=True, exist_ok=True)
    
    @staticmethod
    def get_file_extension(file_path: str) -> str:
        """Get file extension from path"""
        return Path(file_path).suffix.lower()
    
    @staticmethod
    def get_filename_without_extension(file_path: str) -> str:
        """Get filename without extension"""
        return Path(file_path).stem
    
    @staticmethod
    def is_valid_file_extension(file_path: str, allowed_extensions: List[str]) -> bool:
        """Check if file has valid extension"""
        extension = FileUtils.get_file_extension(file_path)
        return extension in [ext.lower() for ext in allowed_extensions]
    
    @staticmethod
    def get_file_size(file_path: str) -> int:
        """Get file size in bytes"""
        try:
            return os.path.getsize(file_path)
        except OSError:
            return 0
    
    @staticmethod
    def copy_file(source_path: str, destination_path: str) -> bool:
        """Copy file from source to destination"""
        try:
            # Ensure destination directory exists
            FileUtils.ensure_directory_exists(os.path.dirname(destination_path))
            shutil.copy2(source_path, destination_path)
            return True
        except Exception as e:
            print(f"Error copying file from {source_path} to {destination_path}: {e}")
            return False
    
    @staticmethod
    def move_file(source_path: str, destination_path: str) -> bool:
        """Move file from source to destination"""
        try:
            # Ensure destination directory exists
            FileUtils.ensure_directory_exists(os.path.dirname(destination_path))
            shutil.move(source_path, destination_path)
            return True
        except Exception as e:
            print(f"Error moving file from {source_path} to {destination_path}: {e}")
            return False
    
    @staticmethod
    def delete_file(file_path: str) -> bool:
        """Delete file if it exists"""
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting file {file_path}: {e}")
            return False
    
    @staticmethod
    def delete_directory(directory_path: str, recursive: bool = False) -> bool:
        """Delete directory"""
        try:
            if os.path.exists(directory_path):
                if recursive:
                    shutil.rmtree(directory_path)
                else:
                    os.rmdir(directory_path)
                return True
            return False
        except Exception as e:
            print(f"Error deleting directory {directory_path}: {e}")
            return False
    
    @staticmethod
    def list_files_in_directory(
        directory_path: str,
        extensions: Optional[List[str]] = None,
        recursive: bool = False
    ) -> List[str]:
        """List files in directory with optional extension filtering"""
        files = []
        
        try:
            if recursive:
                for root, _, filenames in os.walk(directory_path):
                    for filename in filenames:
                        file_path = os.path.join(root, filename)
                        if not extensions or FileUtils.is_valid_file_extension(file_path, extensions):
                            files.append(file_path)
            else:
                for filename in os.listdir(directory_path):
                    file_path = os.path.join(directory_path, filename)
                    if os.path.isfile(file_path):
                        if not extensions or FileUtils.is_valid_file_extension(file_path, extensions):
                            files.append(file_path)
        except Exception as e:
            print(f"Error listing files in {directory_path}: {e}")
        
        return files
    
    @staticmethod
    def get_directory_size(directory_path: str) -> int:
        """Get total size of directory in bytes"""
        total_size = 0
        try:
            for root, _, filenames in os.walk(directory_path):
                for filename in filenames:
                    file_path = os.path.join(root, filename)
                    try:
                        total_size += os.path.getsize(file_path)
                    except OSError:
                        continue
        except Exception as e:
            print(f"Error calculating directory size for {directory_path}: {e}")
        
        return total_size
    
    @staticmethod
    def cleanup_old_files(
        directory_path: str,
        max_age_days: int,
        extensions: Optional[List[str]] = None
    ) -> int:
        """Clean up old files in directory"""
        import time
        
        if not os.path.exists(directory_path):
            return 0
        
        cutoff_time = time.time() - (max_age_days * 24 * 60 * 60)
        deleted_count = 0
        
        try:
            files = FileUtils.list_files_in_directory(directory_path, extensions)
            
            for file_path in files:
                try:
                    if os.path.getmtime(file_path) < cutoff_time:
                        if FileUtils.delete_file(file_path):
                            deleted_count += 1
                            print(f"Deleted old file: {file_path}")
                except OSError:
                    continue
        except Exception as e:
            print(f"Error during cleanup of {directory_path}: {e}")
        
        return deleted_count
    
    @staticmethod
    def safe_filename(filename: str) -> str:
        """Make filename safe for filesystem"""
        # Remove or replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        safe_name = filename
        
        for char in unsafe_chars:
            safe_name = safe_name.replace(char, '_')
        
        # Remove leading/trailing spaces and dots
        safe_name = safe_name.strip(' .')
        
        # Limit length
        if len(safe_name) > 255:
            name, ext = os.path.splitext(safe_name)
            safe_name = name[:255-len(ext)] + ext
        
        return safe_name
    
    @staticmethod
    def get_unique_filename(file_path: str) -> str:
        """Get unique filename by adding number suffix if file exists"""
        if not os.path.exists(file_path):
            return file_path
        
        directory = os.path.dirname(file_path)
        name = FileUtils.get_filename_without_extension(file_path)
        extension = FileUtils.get_file_extension(file_path)
        
        counter = 1
        while True:
            new_name = f"{name}_{counter}{extension}"
            new_path = os.path.join(directory, new_name)
            if not os.path.exists(new_path):
                return new_path
            counter += 1
    
    @staticmethod
    def read_text_file(file_path: str, encoding: str = 'utf-8') -> Optional[str]:
        """Read text file content"""
        try:
            with open(file_path, 'r', encoding=encoding) as f:
                return f.read()
        except Exception as e:
            print(f"Error reading file {file_path}: {e}")
            return None
    
    @staticmethod
    def write_text_file(file_path: str, content: str, encoding: str = 'utf-8') -> bool:
        """Write content to text file"""
        try:
            # Ensure directory exists
            FileUtils.ensure_directory_exists(os.path.dirname(file_path))
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            return True
        except Exception as e:
            print(f"Error writing file {file_path}: {e}")
            return False
