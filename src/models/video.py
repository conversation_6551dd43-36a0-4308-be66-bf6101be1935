"""
Video generation domain models
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass, field
from datetime import datetime
from enum import Enum
import uuid


class VideoJobStatus(Enum):
    """Video job status enumeration"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class VideoGenerationConfig:
    """Configuration for video generation process"""
    # Account settings
    account_name: str
    display_name: str
    color_mode: str = "mode1"
    
    # File paths
    csv_file: Optional[str] = None
    text_box_path: Optional[str] = None
    background_folder: str = "./assets/Back Videos"
    output_folder: str = "./output"
    font_path: str = "./assets/Fonts/NotoSansArabic-Bold.ttf"
    
    # Positioning
    photo_x: int = 1
    photo_y: int = 1
    photo_width: int = 1
    photo_height: int = 1
    
    # Video settings
    num_questions: int = 6
    question_duration: float = 11.0
    answer_display_time: float = 2.5
    last_answer_time: float = 8.5
    green_reveal_time: float = 8.0
    
    # Media settings
    enable_photos: bool = True
    enable_emojis: bool = True
    enable_audio: bool = True
    
    # Quality settings
    video_quality: str = "high"  # low, medium, high
    audio_quality: str = "high"  # low, medium, high
    
    def validate(self) -> bool:
        """Validate the configuration"""
        if not self.account_name or not self.display_name:
            return False
        if self.num_questions <= 0:
            return False
        if self.question_duration <= 0:
            return False
        return True


@dataclass
class VideoConfig:
    """Video output configuration"""
    width: int = 1920
    height: int = 1080
    fps: int = 30
    bitrate: str = "5000k"
    codec: str = "libx264"
    format: str = "mp4"
    
    def get_resolution_string(self) -> str:
        """Get resolution as string (e.g., '1920x1080')"""
        return f"{self.width}x{self.height}"


@dataclass
class VideoJob:
    """Represents a video generation job"""
    id: str
    quiz_id: str
    config: VideoGenerationConfig
    status: VideoJobStatus = VideoJobStatus.PENDING
    progress: int = 0
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    output_path: Optional[str] = None
    error_message: Optional[str] = None
    logs: List[str] = field(default_factory=list)
    created_by: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    def start_processing(self) -> None:
        """Mark job as started"""
        self.status = VideoJobStatus.PROCESSING
        self.started_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.add_log("Video generation started")
    
    def complete_successfully(self, output_path: str) -> None:
        """Mark job as completed successfully"""
        self.status = VideoJobStatus.COMPLETED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.output_path = output_path
        self.progress = 100
        self.add_log(f"Video generation completed: {output_path}")
    
    def fail_with_error(self, error_message: str) -> None:
        """Mark job as failed with error"""
        self.status = VideoJobStatus.FAILED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.error_message = error_message
        self.add_log(f"Video generation failed: {error_message}")
    
    def cancel(self) -> None:
        """Cancel the job"""
        self.status = VideoJobStatus.CANCELLED
        self.completed_at = datetime.utcnow()
        self.updated_at = datetime.utcnow()
        self.add_log("Video generation cancelled")
    
    def update_progress(self, progress: int, message: Optional[str] = None) -> None:
        """Update job progress"""
        self.progress = max(0, min(100, progress))
        self.updated_at = datetime.utcnow()
        if message:
            self.add_log(f"Progress {progress}%: {message}")
    
    def add_log(self, message: str) -> None:
        """Add a log entry"""
        timestamp = datetime.utcnow().isoformat()
        log_entry = f"[{timestamp}] {message}"
        self.logs.append(log_entry)
    
    @property
    def is_completed(self) -> bool:
        """Check if job is completed (successfully or with error)"""
        return self.status in [VideoJobStatus.COMPLETED, VideoJobStatus.FAILED, VideoJobStatus.CANCELLED]
    
    @property
    def is_processing(self) -> bool:
        """Check if job is currently processing"""
        return self.status == VideoJobStatus.PROCESSING
    
    @property
    def duration_seconds(self) -> Optional[float]:
        """Get job duration in seconds"""
        if self.started_at and self.completed_at:
            return (self.completed_at - self.started_at).total_seconds()
        return None
