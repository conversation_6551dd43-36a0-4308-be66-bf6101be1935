"""
Media-related domain models
"""

from typing import Optional, List, Dict, Any, Tuple
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import uuid


class MediaType(Enum):
    """Media type enumeration"""
    PHOTO = "photo"
    EMOJI = "emoji"
    AUDIO = "audio"
    VIDEO = "video"


class PhotoSource(Enum):
    """Photo source enumeration"""
    UNSPLASH = "unsplash"
    PIXABAY = "pixabay"
    PEXELS = "pexels"
    LOCAL = "local"
    FALLBACK = "fallback"


class EmojiStyle(Enum):
    """Emoji style enumeration"""
    STANDARD = "standard"
    PLAYFUL = "playful"


@dataclass
class Photo:
    """Represents a photo used in video generation"""
    id: str
    url: str
    local_path: Optional[str] = None
    source: PhotoSource = PhotoSource.UNSPLASH
    search_term: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    file_size: Optional[int] = None
    downloaded_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
    
    @property
    def is_downloaded(self) -> bool:
        """Check if photo is downloaded locally"""
        return self.local_path is not None and self.downloaded_at is not None
    
    @property
    def dimensions(self) -> Optional[Tuple[int, int]]:
        """Get photo dimensions as tuple"""
        if self.width and self.height:
            return (self.width, self.height)
        return None
    
    def mark_downloaded(self, local_path: str, width: int = None, height: int = None, file_size: int = None) -> None:
        """Mark photo as downloaded"""
        self.local_path = local_path
        self.downloaded_at = datetime.utcnow()
        if width:
            self.width = width
        if height:
            self.height = height
        if file_size:
            self.file_size = file_size


@dataclass
class Emoji:
    """Represents an emoji used in video generation"""
    id: str
    code: str
    local_path: Optional[str] = None
    style: EmojiStyle = EmojiStyle.STANDARD
    search_term: Optional[str] = None
    size: int = 120
    downloaded_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
    
    @property
    def is_downloaded(self) -> bool:
        """Check if emoji is downloaded locally"""
        return self.local_path is not None and self.downloaded_at is not None
    
    def mark_downloaded(self, local_path: str) -> None:
        """Mark emoji as downloaded"""
        self.local_path = local_path
        self.downloaded_at = datetime.utcnow()


@dataclass
class AudioFile:
    """Represents an audio file used in video generation"""
    id: str
    text: str
    local_path: Optional[str] = None
    language: str = "ar"
    voice_id: Optional[str] = None
    duration: Optional[float] = None
    file_size: Optional[int] = None
    generated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
    
    @property
    def is_generated(self) -> bool:
        """Check if audio file is generated"""
        return self.local_path is not None and self.generated_at is not None
    
    def mark_generated(self, local_path: str, duration: float = None, file_size: int = None) -> None:
        """Mark audio as generated"""
        self.local_path = local_path
        self.generated_at = datetime.utcnow()
        if duration:
            self.duration = duration
        if file_size:
            self.file_size = file_size


@dataclass
class MediaCollection:
    """Collection of media items for a video generation job"""
    photos: List[Photo]
    emojis: List[Emoji]
    audio_files: List[AudioFile]
    
    def __post_init__(self):
        if self.photos is None:
            self.photos = []
        if self.emojis is None:
            self.emojis = []
        if self.audio_files is None:
            self.audio_files = []
    
    @property
    def total_items(self) -> int:
        """Get total number of media items"""
        return len(self.photos) + len(self.emojis) + len(self.audio_files)
    
    @property
    def downloaded_photos_count(self) -> int:
        """Get count of downloaded photos"""
        return sum(1 for photo in self.photos if photo.is_downloaded)
    
    @property
    def downloaded_emojis_count(self) -> int:
        """Get count of downloaded emojis"""
        return sum(1 for emoji in self.emojis if emoji.is_downloaded)
    
    @property
    def generated_audio_count(self) -> int:
        """Get count of generated audio files"""
        return sum(1 for audio in self.audio_files if audio.is_generated)
    
    def add_photo(self, photo: Photo) -> None:
        """Add a photo to the collection"""
        self.photos.append(photo)
    
    def add_emoji(self, emoji: Emoji) -> None:
        """Add an emoji to the collection"""
        self.emojis.append(emoji)
    
    def add_audio(self, audio: AudioFile) -> None:
        """Add an audio file to the collection"""
        self.audio_files.append(audio)
    
    def get_photos_by_search_term(self, search_term: str) -> List[Photo]:
        """Get photos by search term"""
        return [photo for photo in self.photos if photo.search_term == search_term]
    
    def get_emojis_by_search_term(self, search_term: str) -> List[Emoji]:
        """Get emojis by search term"""
        return [emoji for emoji in self.emojis if emoji.search_term == search_term]
