"""
Positioning and layout configuration models
"""

from typing import Dict, <PERSON>, <PERSON><PERSON>, Optional
from dataclasses import dataclass
from enum import Enum


class ColorMode(Enum):
    """Color mode enumeration"""
    MODE1 = "mode1"  # White backgrounds
    MODE2 = "mode2"  # Black backgrounds  
    MODE3 = "mode3"  # Mixed mode 1
    MODE4 = "mode4"  # Mixed mode 2


@dataclass
class PositionConfig:
    """Position configuration for text and elements"""
    x: int
    y: int
    width: Optional[int] = None
    height: Optional[int] = None
    
    def to_tuple(self) -> Tuple[int, int]:
        """Get position as tuple"""
        return (self.x, self.y)
    
    def to_dict(self) -> Dict[str, int]:
        """Convert to dictionary"""
        data = {'x': self.x, 'y': self.y}
        if self.width is not None:
            data['width'] = self.width
        if self.height is not None:
            data['height'] = self.height
        return data


@dataclass
class TextBoxConfig:
    """Configuration for text boxes"""
    width: int
    height: int
    x: int
    y: int
    corner_radius: int = 30
    border_width: int = 7
    fill_color: Tuple[int, int, int] = (255, 255, 255)  # White
    text_color: Tuple[int, int, int] = (0, 0, 0)  # Black
    border_color: Tuple[int, int, int] = (0, 0, 0)  # Black
    shadow_color: Tuple[int, int, int, int] = (0, 0, 0, 180)  # Black with alpha
    shadow_offset: Tuple[int, int] = (3, 3)
    
    # Letter circle settings
    letter_circle_radius: int = 80
    letter_circle_color: Tuple[int, int, int] = (138, 43, 226)  # Purple
    letter_text_color: Tuple[int, int, int] = (255, 255, 255)  # White
    letter_offset_x: int = -370
    
    # Text settings
    option_text_size: int = 80
    letter_text_size: int = 85
    text_x_offset: int = 60
    text_y_offset: int = -20
    
    # Letter positioning inside circle
    letter_x_offset: int = 0
    letter_y_offset: int = -12
    
    # Correct answer colors
    correct_fill_color: Tuple[int, int, int] = (34, 197, 94)  # Green
    correct_border_color: Tuple[int, int, int] = (22, 163, 74)  # Dark green
    correct_letter_circle_color: Tuple[int, int, int] = (21, 128, 61)  # Dark green
    
    def get_position(self) -> PositionConfig:
        """Get position configuration"""
        return PositionConfig(x=self.x, y=self.y, width=self.width, height=self.height)
    
    def get_shadow_position(self) -> Tuple[int, int]:
        """Get shadow position"""
        return (self.x + self.shadow_offset[0], self.y + self.shadow_offset[1])


@dataclass
class QuestionTextBoxConfig:
    """Configuration for question text boxes"""
    width: int = 920
    height: int = 530
    x: int = 90
    y: int = 235
    corner_radius: int = 50
    border_width: int = 10
    fill_color: Tuple[int, int, int] = (255, 255, 255)  # White
    text_color: Tuple[int, int, int] = (0, 0, 0)  # Black
    shadow_color: Tuple[int, int, int, int] = (0, 0, 0, 150)  # Black with alpha
    shadow_offset: Tuple[int, int] = (2, 2)
    
    def get_position(self) -> PositionConfig:
        """Get position configuration"""
        return PositionConfig(x=self.x, y=self.y, width=self.width, height=self.height)


@dataclass
class PhotoConfig:
    """Configuration for photos in video"""
    width: int = 250
    height: int = 250
    spacing: int = 30
    total_width: int = 840
    x_start: int = 140
    y: int = 450
    
    def get_photo_positions(self, num_photos: int = 3) -> List[PositionConfig]:
        """Get positions for multiple photos"""
        positions = []
        for i in range(num_photos):
            x = self.x_start + i * (self.width + self.spacing)
            positions.append(PositionConfig(x=x, y=self.y, width=self.width, height=self.height))
        return positions


@dataclass
class EmojiConfig:
    """Configuration for emojis in video"""
    size: int = 120
    x_offset: int = -20
    y_base: int = 860
    spacing: int = 180
    
    def get_emoji_positions(self, num_emojis: int = 3) -> List[PositionConfig]:
        """Get positions for multiple emojis"""
        positions = []
        for i in range(num_emojis):
            y = self.y_base + i * self.spacing
            positions.append(PositionConfig(x=self.x_offset, y=y, width=self.size, height=self.size))
        return positions


@dataclass
class AccountNameTextBoxConfig:
    """Configuration for account name text box"""
    width: int = 1150
    height: int = 120
    x: int = -25
    y: int = 160
    corner_radius: int = 20
    border_width: int = 0
    text_color: Tuple[int, int, int] = (255, 255, 255)  # White
    shadow_color: Tuple[int, int, int, int] = (0, 0, 0, 100)  # Black with alpha
    shadow_offset: Tuple[int, int] = (2, 2)
    font_size: int = 75
    gradient_start: Tuple[int, int, int] = (0, 0, 0)  # Black
    gradient_end: Tuple[int, int, int] = (0, 0, 0)  # Black
    
    def get_position(self) -> PositionConfig:
        """Get position configuration"""
        return PositionConfig(x=self.x, y=self.y, width=self.width, height=self.height)


@dataclass
class ColorModeConfig:
    """Color configuration for different modes"""
    question_text_box: Dict[str, Any]
    options_text_box: Dict[str, Any]
    
    @classmethod
    def get_mode_config(cls, mode: ColorMode) -> 'ColorModeConfig':
        """Get configuration for specific color mode"""
        configs = {
            ColorMode.MODE1: cls(
                question_text_box={
                    "fill_color": (255, 255, 255),  # White fill
                    "text_color": (0, 0, 0),  # Black text
                    "shadow_color": (0, 0, 0, 150),  # Black shadow
                },
                options_text_box={
                    "fill_color": (255, 255, 255),  # White fill
                    "text_color": (0, 0, 0),  # Black text
                    "border_color": (0, 0, 0),  # Black stroke
                    "shadow_color": (0, 0, 0, 180),  # Black shadow
                }
            ),
            ColorMode.MODE2: cls(
                question_text_box={
                    "fill_color": (0, 0, 0),  # Black fill
                    "text_color": (255, 255, 255),  # White text
                    "shadow_color": (255, 255, 255, 150),  # White shadow
                },
                options_text_box={
                    "fill_color": (0, 0, 0),  # Black fill
                    "text_color": (255, 255, 255),  # White text
                    "border_color": (255, 255, 255),  # White stroke
                    "shadow_color": (255, 255, 255, 180),  # White shadow
                }
            ),
            ColorMode.MODE3: cls(
                question_text_box={
                    "fill_color": (255, 255, 255),  # White fill
                    "text_color": (0, 0, 0),  # Black text
                    "shadow_color": (0, 0, 0, 150),  # Black shadow
                },
                options_text_box={
                    "fill_color": (0, 0, 0),  # Black fill
                    "text_color": (255, 255, 255),  # White text
                    "border_color": (255, 255, 255),  # White stroke
                    "shadow_color": (255, 255, 255, 180),  # White shadow
                }
            ),
            ColorMode.MODE4: cls(
                question_text_box={
                    "fill_color": (0, 0, 0),  # Black fill
                    "text_color": (255, 255, 255),  # White text
                    "shadow_color": (255, 255, 255, 150),  # White shadow
                },
                options_text_box={
                    "fill_color": (255, 255, 255),  # White fill
                    "text_color": (0, 0, 0),  # Black text
                    "border_color": (0, 0, 0),  # Black stroke
                    "shadow_color": (0, 0, 0, 180),  # Black shadow
                }
            )
        }
        return configs.get(mode, configs[ColorMode.MODE1])
