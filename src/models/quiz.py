"""
Quiz domain models
"""

from typing import List, Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import uuid


@dataclass
class Question:
    """Represents a quiz question with options and correct answer"""
    id: str
    text: str
    options: List[str]
    correct_answer: str
    explanation: Optional[str] = None
    order_index: int = 0
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
    
    @property
    def option_count(self) -> int:
        """Get the number of options for this question"""
        return len(self.options)
    
    def get_correct_option_letter(self) -> str:
        """Extract the correct option letter (A, B, C) from the correct answer"""
        import re
        # Look for patterns like "Answer A", "Answer B", "Answer C"
        match = re.search(r'Answer\s+([ABC])', self.correct_answer, re.IGNORECASE)
        if match:
            return match.group(1).upper()
        
        # Look for just the letter
        match = re.search(r'^([ABC])$', self.correct_answer.strip(), re.IGNORECASE)
        if match:
            return match.group(1).upper()
        
        # Default to A if can't determine
        return 'A'
    
    def extract_option_text(self, option: str) -> str:
        """Extract the text from an option, removing the A), B), C) prefix"""
        if ')' in option:
            return option.split(')', 1)[1].strip()
        return option.strip()


@dataclass
class Quiz:
    """Represents a complete quiz with questions and metadata"""
    id: str
    title: str
    description: Optional[str] = None
    language: str = "ar"
    questions: List[Question] = None
    created_by: Optional[str] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.questions is None:
            self.questions = []
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    @property
    def question_count(self) -> int:
        """Get the number of questions in this quiz"""
        return len(self.questions)
    
    def add_question(self, question: Question) -> None:
        """Add a question to the quiz"""
        question.order_index = len(self.questions)
        self.questions.append(question)
        self.updated_at = datetime.utcnow()
    
    def get_questions_for_video(self, num_questions: int = 6) -> List[Question]:
        """Get a specific number of questions for video generation"""
        if len(self.questions) <= num_questions:
            return self.questions.copy()
        
        # Return first num_questions questions, maintaining order
        return self.questions[:num_questions]


@dataclass
class QuizConfig:
    """Configuration for quiz processing and video generation"""
    num_questions: int = 6
    question_duration: float = 11.0  # seconds per question
    answer_display_time: float = 2.5  # seconds to show answer
    last_answer_time: float = 8.5  # when answer appears
    green_reveal_time: float = 8.0  # when to show green highlight
    
    # Language settings
    source_language: Optional[str] = None
    target_language: str = "en"
    
    # Video settings
    video_width: int = 1920
    video_height: int = 1080
    
    def get_total_duration(self) -> float:
        """Calculate total video duration"""
        return self.num_questions * self.question_duration
    
    def validate(self) -> bool:
        """Validate configuration values"""
        if self.num_questions <= 0:
            return False
        if self.question_duration <= 0:
            return False
        if self.answer_display_time <= 0:
            return False
        if self.last_answer_time >= self.question_duration:
            return False
        if self.green_reveal_time >= self.question_duration:
            return False
        return True
