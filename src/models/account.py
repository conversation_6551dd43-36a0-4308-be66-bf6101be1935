"""
Account and user domain models
"""

from typing import Optional, Dict, Any
from dataclasses import dataclass
from datetime import datetime
import uuid


@dataclass
class AccountConfig:
    """Configuration for a video generation account"""
    name: str
    display_name: str
    color_mode: str = "mode1"
    csv_file: Optional[str] = None
    text_box_path: Optional[str] = None
    background_folder: str = "./assets/Back Videos"
    output_folder: str = "./output"
    font_path: str = "./assets/Fonts/NotoSansArabic-Bold.ttf"
    
    # Positioning settings
    photo_x: int = 1
    photo_y: int = 1
    photo_width: int = 1
    photo_height: int = 1
    
    def __post_init__(self):
        if not self.name:
            raise ValueError("Account name is required")
        if not self.display_name:
            raise ValueError("Display name is required")
    
    @property
    def account_name(self) -> str:
        """Alias for name property for backward compatibility"""
        return self.name
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        return {
            'name': self.name,
            'display_name': self.display_name,
            'color_mode': self.color_mode,
            'csv_file': self.csv_file,
            'text_box_path': self.text_box_path,
            'background_folder': self.background_folder,
            'output_folder': self.output_folder,
            'font_path': self.font_path,
            'photo_x': self.photo_x,
            'photo_y': self.photo_y,
            'photo_width': self.photo_width,
            'photo_height': self.photo_height
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'AccountConfig':
        """Create from dictionary"""
        return cls(**data)


@dataclass
class Account:
    """Represents a user account in the system"""
    id: str
    email: str
    username: str
    full_name: str
    is_active: bool = True
    is_verified: bool = False
    role: str = "user"
    avatar_url: Optional[str] = None
    last_login: Optional[datetime] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    def __post_init__(self):
        if not self.id:
            self.id = str(uuid.uuid4())
        if self.created_at is None:
            self.created_at = datetime.utcnow()
        if self.updated_at is None:
            self.updated_at = datetime.utcnow()
    
    @property
    def is_admin(self) -> bool:
        """Check if user is admin"""
        return self.role == "admin"
    
    @property
    def is_moderator(self) -> bool:
        """Check if user is moderator"""
        return self.role in ["admin", "moderator"]
    
    def update_last_login(self) -> None:
        """Update last login timestamp"""
        self.last_login = datetime.utcnow()
        self.updated_at = datetime.utcnow()
    
    def activate(self) -> None:
        """Activate the account"""
        self.is_active = True
        self.updated_at = datetime.utcnow()
    
    def deactivate(self) -> None:
        """Deactivate the account"""
        self.is_active = False
        self.updated_at = datetime.utcnow()
    
    def verify_email(self) -> None:
        """Mark email as verified"""
        self.is_verified = True
        self.updated_at = datetime.utcnow()
    
    def to_dict(self, include_sensitive: bool = False) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = {
            'id': self.id,
            'email': self.email,
            'username': self.username,
            'full_name': self.full_name,
            'is_active': self.is_active,
            'is_verified': self.is_verified,
            'role': self.role,
            'avatar_url': self.avatar_url,
            'last_login': self.last_login.isoformat() if self.last_login else None,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'updated_at': self.updated_at.isoformat() if self.updated_at else None
        }
        
        return data
