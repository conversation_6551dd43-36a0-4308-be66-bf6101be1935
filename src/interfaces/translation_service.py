"""
Translation service interface
"""

from abc import ABC, abstractmethod
from typing import Optional, List


class ITranslationService(ABC):
    """Interface for translation services"""
    
    @abstractmethod
    async def translate_text(
        self,
        text: str,
        target_language: str = "en",
        source_language: Optional[str] = None
    ) -> str:
        """
        Translate text to target language
        
        Args:
            text: Text to translate
            target_language: Target language code (e.g., 'en', 'ar', 'es')
            source_language: Source language code (auto-detect if None)
            
        Returns:
            Translated text
            
        Raises:
            TranslationError: If translation fails
            UnsupportedLanguageError: If language is not supported
        """
        pass
    
    @abstractmethod
    async def detect_language(self, text: str) -> str:
        """
        Detect the language of given text
        
        Args:
            text: Text to analyze
            
        Returns:
            Language code (e.g., 'en', 'ar', 'es')
            
        Raises:
            LanguageDetectionError: If language detection fails
        """
        pass
    
    @abstractmethod
    async def translate_batch(
        self,
        texts: List[str],
        target_language: str = "en",
        source_language: Optional[str] = None
    ) -> List[str]:
        """
        Translate multiple texts in batch
        
        Args:
            texts: List of texts to translate
            target_language: Target language code
            source_language: Source language code (auto-detect if None)
            
        Returns:
            List of translated texts
            
        Raises:
            TranslationError: If translation fails
        """
        pass
    
    @abstractmethod
    def get_supported_languages(self) -> List[str]:
        """
        Get list of supported language codes
        
        Returns:
            List of supported language codes
        """
        pass
    
    @abstractmethod
    def is_language_supported(self, language_code: str) -> bool:
        """
        Check if language is supported
        
        Args:
            language_code: Language code to check
            
        Returns:
            True if language is supported
        """
        pass
    
    @abstractmethod
    async def get_usage_info(self) -> dict:
        """
        Get translation service usage information
        
        Returns:
            Dictionary with usage information (characters used, limit, etc.)
        """
        pass
