"""
Video generator interface
"""

from abc import ABC, abstractmethod
from typing import List, Optional
from ..models.quiz import Quiz, Question
from ..models.video import VideoJob, VideoGenerationConfig
from ..models.account import AccountConfig


class IVideoGenerator(ABC):
    """Interface for video generation services"""
    
    @abstractmethod
    async def generate_video_from_quiz(
        self,
        quiz: Quiz,
        config: VideoGenerationConfig,
        account_config: AccountConfig,
        output_path: str,
        num_questions: int = 6
    ) -> str:
        """
        Generate video from quiz data
        
        Args:
            quiz: Quiz object containing questions
            config: Video generation configuration
            account_config: Account configuration
            output_path: Path where video should be saved
            num_questions: Number of questions to include
            
        Returns:
            Path to generated video file
            
        Raises:
            VideoGenerationError: If video generation fails
        """
        pass
    
    @abstractmethod
    async def generate_video_from_csv(
        self,
        csv_file_path: str,
        account_config: AccountConfig,
        output_path: str,
        num_questions: int = 6
    ) -> str:
        """
        Generate video from CSV file
        
        Args:
            csv_file_path: Path to CSV file containing questions
            account_config: Account configuration
            output_path: Path where video should be saved
            num_questions: Number of questions to include
            
        Returns:
            Path to generated video file
            
        Raises:
            VideoGenerationError: If video generation fails
            CSVParsingError: If CSV parsing fails
        """
        pass
    
    @abstractmethod
    async def process_video_job(self, job: VideoJob) -> VideoJob:
        """
        Process a video generation job
        
        Args:
            job: Video job to process
            
        Returns:
            Updated video job with results
            
        Raises:
            VideoGenerationError: If video generation fails
        """
        pass
    
    @abstractmethod
    def get_supported_formats(self) -> List[str]:
        """
        Get list of supported video formats
        
        Returns:
            List of supported format extensions
        """
        pass
    
    @abstractmethod
    def validate_config(self, config: VideoGenerationConfig) -> bool:
        """
        Validate video generation configuration
        
        Args:
            config: Configuration to validate
            
        Returns:
            True if configuration is valid
            
        Raises:
            ValidationError: If configuration is invalid
        """
        pass
    
    @abstractmethod
    async def estimate_generation_time(
        self,
        num_questions: int,
        config: VideoGenerationConfig
    ) -> float:
        """
        Estimate video generation time in seconds
        
        Args:
            num_questions: Number of questions
            config: Video generation configuration
            
        Returns:
            Estimated time in seconds
        """
        pass
    
    @abstractmethod
    async def cancel_job(self, job_id: str) -> bool:
        """
        Cancel a running video generation job
        
        Args:
            job_id: ID of job to cancel
            
        Returns:
            True if job was cancelled successfully
        """
        pass
