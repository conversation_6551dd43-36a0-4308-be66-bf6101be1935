"""
Media service interfaces
"""

from abc import ABC, abstractmethod
from typing import List, Optional, Dict, Any
from ..models.media import Photo, Emoji, AudioFile, MediaCollection


class IMediaService(ABC):
    """Base interface for media services"""
    
    @abstractmethod
    async def download_media(self, url: str, local_path: str) -> bool:
        """
        Download media from URL to local path
        
        Args:
            url: URL to download from
            local_path: Local path to save to
            
        Returns:
            True if download successful
            
        Raises:
            MediaDownloadError: If download fails
        """
        pass
    
    @abstractmethod
    def validate_url(self, url: str) -> bool:
        """
        Validate media URL
        
        Args:
            url: URL to validate
            
        Returns:
            True if URL is valid
        """
        pass


class IPhotoService(IMediaService):
    """Interface for photo services"""
    
    @abstractmethod
    async def search_photos(
        self,
        search_term: str,
        num_photos: int = 3,
        language: Optional[str] = None
    ) -> List[Photo]:
        """
        Search for photos by term
        
        Args:
            search_term: Term to search for
            num_photos: Number of photos to return
            language: Language for search (optional)
            
        Returns:
            List of Photo objects
            
        Raises:
            PhotoAPIError: If search fails
        """
        pass
    
    @abstractmethod
    async def get_photos_for_options(
        self,
        options: List[str],
        question_index: int,
        language: Optional[str] = None
    ) -> List[Photo]:
        """
        Get photos for quiz options
        
        Args:
            options: List of option texts
            question_index: Index of the question
            language: Language of the options
            
        Returns:
            List of Photo objects
            
        Raises:
            PhotoAPIError: If photo retrieval fails
        """
        pass
    
    @abstractmethod
    async def get_fallback_photos(self, num_photos: int = 3) -> List[Photo]:
        """
        Get fallback photos when search fails
        
        Args:
            num_photos: Number of fallback photos needed
            
        Returns:
            List of fallback Photo objects
        """
        pass
    
    @abstractmethod
    def get_available_apis(self) -> List[str]:
        """
        Get list of available photo APIs
        
        Returns:
            List of API names
        """
        pass


class IEmojiService(IMediaService):
    """Interface for emoji services"""
    
    @abstractmethod
    async def get_emoji_for_text(
        self,
        text: str,
        style: str = "standard"
    ) -> Optional[Emoji]:
        """
        Get emoji for given text
        
        Args:
            text: Text to find emoji for
            style: Emoji style ('standard' or 'playful')
            
        Returns:
            Emoji object or None if not found
            
        Raises:
            EmojiError: If emoji processing fails
        """
        pass
    
    @abstractmethod
    async def get_emojis_for_options(
        self,
        options: List[str],
        question_index: int,
        account_index: int = 0,
        video_index: int = 0
    ) -> List[Emoji]:
        """
        Get emojis for quiz options
        
        Args:
            options: List of option texts
            question_index: Index of the question
            account_index: Account index for caching
            video_index: Video index for caching
            
        Returns:
            List of Emoji objects
            
        Raises:
            EmojiError: If emoji processing fails
        """
        pass
    
    @abstractmethod
    async def create_fallback_emoji(
        self,
        emoji_code: str,
        local_path: str,
        style: str = "standard"
    ) -> Optional[Emoji]:
        """
        Create fallback emoji when download fails
        
        Args:
            emoji_code: Unicode emoji code
            local_path: Path to save fallback emoji
            style: Emoji style
            
        Returns:
            Emoji object or None if creation fails
        """
        pass


class IAudioService(IMediaService):
    """Interface for audio services"""
    
    @abstractmethod
    async def generate_speech(
        self,
        text: str,
        language: str = "ar",
        voice_id: Optional[str] = None
    ) -> AudioFile:
        """
        Generate speech from text
        
        Args:
            text: Text to convert to speech
            language: Language code
            voice_id: Specific voice ID (optional)
            
        Returns:
            AudioFile object
            
        Raises:
            AudioGenerationError: If speech generation fails
        """
        pass
    
    @abstractmethod
    async def generate_audio_files(
        self,
        questions: List[str],
        correct_answers: List[str],
        language: str = "ar"
    ) -> Dict[str, AudioFile]:
        """
        Generate audio files for questions and answers
        
        Args:
            questions: List of question texts
            correct_answers: List of correct answer texts
            language: Language code
            
        Returns:
            Dictionary mapping question/answer to AudioFile
            
        Raises:
            AudioGenerationError: If audio generation fails
        """
        pass
    
    @abstractmethod
    def get_available_voices(self, language: str = "ar") -> List[Dict[str, Any]]:
        """
        Get available voices for language
        
        Args:
            language: Language code
            
        Returns:
            List of voice information dictionaries
        """
        pass
    
    @abstractmethod
    async def get_audio_duration(self, audio_path: str) -> float:
        """
        Get duration of audio file in seconds
        
        Args:
            audio_path: Path to audio file
            
        Returns:
            Duration in seconds
        """
        pass
