"""
Interfaces for Xvion Video Generator

This module contains abstract base classes and interfaces that define contracts
for various services and components in the application.
"""

from .video_generator import IVideoGenerator
from .translation_service import ITranslationService
from .media_service import IMediaService, IPhotoService, IEmojiService, IAudioService
from .text_processor import ITextProcessor
from .repository import IRepository, IQuizRepository, IVideoJobRepository

__all__ = [
    'IVideoGenerator',
    'ITranslationService',
    'IMediaService',
    'IPhotoService',
    'IEmojiService',
    'IAudioService',
    'ITextProcessor',
    'IRepository',
    'IQuizRepository',
    'IVideoJobRepository'
]
