"""
API keys configuration
"""

from typing import Dict, List, Optional
from .base import BaseConfig


class APIKeysConfig(BaseConfig):
    """API keys configuration"""
    
    def __init__(self):
        # Translation API
        self.deepl_auth_key: Optional[str] = None
        
        # Text-to-Speech API
        self.elevenlabs_api_key: Optional[str] = None
        
        # Photo APIs
        self.unsplash_api_keys: List[str] = []
        self.pixabay_api_key: Optional[str] = None
        self.pexels_api_key: Optional[str] = None
        
        # AI APIs
        self.openai_api_key: Optional[str] = None
        
        # Other APIs
        self.custom_api_keys: Dict[str, str] = {}
        
        super().__init__()
    
    def _load_from_environment(self) -> None:
        """Load API keys from environment variables"""
        # Translation
        self.deepl_auth_key = self.get_env_var('DEEPL_AUTH_KEY')
        
        # Text-to-Speech
        self.elevenlabs_api_key = self.get_env_var('ELEVENLABS_API_KEY')
        
        # Photo APIs
        unsplash_keys = self.get_env_var('UNSPLASH_API_KEYS', '')
        if unsplash_keys:
            self.unsplash_api_keys = [key.strip() for key in unsplash_keys.split(',') if key.strip()]
        
        self.pixabay_api_key = self.get_env_var('PIXABAY_API_KEY')
        self.pexels_api_key = self.get_env_var('PEXELS_API_KEY')
        
        # AI APIs
        self.openai_api_key = self.get_env_var('OPENAI_API_KEY')
        
        # Load custom API keys (format: CUSTOM_API_KEY_NAME=value)
        import os
        for key, value in os.environ.items():
            if key.startswith('CUSTOM_API_KEY_'):
                api_name = key.replace('CUSTOM_API_KEY_', '').lower()
                self.custom_api_keys[api_name] = value
    
    def _validate_config(self) -> None:
        """Validate API keys configuration"""
        # Check for required API keys
        required_keys = {
            'elevenlabs_api_key': 'ElevenLabs API key is required for text-to-speech',
        }
        
        for key, message in required_keys.items():
            if not getattr(self, key):
                print(f"Warning: {message}")
        
        # Validate API key formats (basic validation)
        if self.deepl_auth_key and not self.deepl_auth_key.endswith(':fx'):
            print("Warning: DeepL API key format may be incorrect")
        
        if self.elevenlabs_api_key and len(self.elevenlabs_api_key) < 20:
            print("Warning: ElevenLabs API key format may be incorrect")
    
    @property
    def has_translation_api(self) -> bool:
        """Check if translation API is available"""
        return bool(self.deepl_auth_key)
    
    @property
    def has_tts_api(self) -> bool:
        """Check if text-to-speech API is available"""
        return bool(self.elevenlabs_api_key)
    
    @property
    def has_photo_apis(self) -> bool:
        """Check if any photo API is available"""
        return bool(self.unsplash_api_keys or self.pixabay_api_key or self.pexels_api_key)
    
    @property
    def available_photo_apis(self) -> List[str]:
        """Get list of available photo APIs"""
        apis = []
        if self.unsplash_api_keys:
            apis.append('unsplash')
        if self.pixabay_api_key:
            apis.append('pixabay')
        if self.pexels_api_key:
            apis.append('pexels')
        return apis
    
    def get_unsplash_api_key(self, index: int = 0) -> Optional[str]:
        """Get Unsplash API key by index (for rotation)"""
        if not self.unsplash_api_keys:
            return None
        
        # Use modulo to cycle through available keys
        key_index = index % len(self.unsplash_api_keys)
        return self.unsplash_api_keys[key_index]
    
    def get_api_key(self, service: str) -> Optional[str]:
        """Get API key for a specific service"""
        service_map = {
            'deepl': self.deepl_auth_key,
            'elevenlabs': self.elevenlabs_api_key,
            'pixabay': self.pixabay_api_key,
            'pexels': self.pexels_api_key,
            'openai': self.openai_api_key,
        }
        
        # Check standard services
        if service in service_map:
            return service_map[service]
        
        # Check Unsplash (special case)
        if service == 'unsplash':
            return self.get_unsplash_api_key()
        
        # Check custom API keys
        if service in self.custom_api_keys:
            return self.custom_api_keys[service]
        
        return None
    
    def set_api_key(self, service: str, key: str) -> None:
        """Set API key for a specific service"""
        if service == 'deepl':
            self.deepl_auth_key = key
        elif service == 'elevenlabs':
            self.elevenlabs_api_key = key
        elif service == 'pixabay':
            self.pixabay_api_key = key
        elif service == 'pexels':
            self.pexels_api_key = key
        elif service == 'openai':
            self.openai_api_key = key
        elif service == 'unsplash':
            if key not in self.unsplash_api_keys:
                self.unsplash_api_keys.append(key)
        else:
            # Store as custom API key
            self.custom_api_keys[service] = key
    
    def remove_api_key(self, service: str) -> bool:
        """Remove API key for a specific service"""
        if service == 'deepl':
            self.deepl_auth_key = None
            return True
        elif service == 'elevenlabs':
            self.elevenlabs_api_key = None
            return True
        elif service == 'pixabay':
            self.pixabay_api_key = None
            return True
        elif service == 'pexels':
            self.pexels_api_key = None
            return True
        elif service == 'openai':
            self.openai_api_key = None
            return True
        elif service == 'unsplash':
            self.unsplash_api_keys.clear()
            return True
        elif service in self.custom_api_keys:
            del self.custom_api_keys[service]
            return True
        
        return False
    
    def to_dict(self, include_keys: bool = False) -> Dict[str, any]:
        """Convert to dictionary, optionally including actual keys"""
        data = super().to_dict()
        
        if not include_keys:
            # Mask API keys for security
            for key in data:
                if 'key' in key.lower() and data[key]:
                    if isinstance(data[key], str):
                        data[key] = f"{data[key][:4]}...{data[key][-4:]}" if len(data[key]) > 8 else "***"
                    elif isinstance(data[key], list):
                        data[key] = [f"{k[:4]}...{k[-4:]}" if len(k) > 8 else "***" for k in data[key]]
        
        return data
