"""
Base configuration class
"""

import os
from typing import Dict, Any, Optional
from abc import ABC, abstractmethod


class BaseConfig(ABC):
    """Base configuration class with common functionality"""
    
    def __init__(self):
        self._load_from_environment()
        self._validate_config()
    
    @abstractmethod
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        pass
    
    @abstractmethod
    def _validate_config(self) -> None:
        """Validate configuration values"""
        pass
    
    def get_env_var(self, key: str, default: Any = None, required: bool = False) -> Any:
        """Get environment variable with optional default and required check"""
        value = os.getenv(key, default)
        
        if required and value is None:
            raise ValueError(f"Required environment variable '{key}' is not set")
        
        return value
    
    def get_env_bool(self, key: str, default: bool = False) -> bool:
        """Get boolean environment variable"""
        value = self.get_env_var(key, str(default))
        return value.lower() in ('true', '1', 'yes', 'on')
    
    def get_env_int(self, key: str, default: int = 0) -> int:
        """Get integer environment variable"""
        value = self.get_env_var(key, str(default))
        try:
            return int(value)
        except ValueError:
            return default
    
    def get_env_float(self, key: str, default: float = 0.0) -> float:
        """Get float environment variable"""
        value = self.get_env_var(key, str(default))
        try:
            return float(value)
        except ValueError:
            return default
    
    def get_env_list(self, key: str, default: list = None, separator: str = ',') -> list:
        """Get list environment variable"""
        if default is None:
            default = []
        
        value = self.get_env_var(key)
        if not value:
            return default
        
        return [item.strip() for item in value.split(separator) if item.strip()]
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert configuration to dictionary"""
        return {
            key: value for key, value in self.__dict__.items()
            if not key.startswith('_')
        }
    
    def update_from_dict(self, data: Dict[str, Any]) -> None:
        """Update configuration from dictionary"""
        for key, value in data.items():
            if hasattr(self, key):
                setattr(self, key, value)
    
    def __repr__(self) -> str:
        """String representation of configuration"""
        class_name = self.__class__.__name__
        attrs = ', '.join(f'{k}={v}' for k, v in self.to_dict().items())
        return f'{class_name}({attrs})'
