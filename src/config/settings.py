"""
Main settings configuration that combines all other configurations
"""

import os
from typing import Optional
from .base import BaseConfig
from .video import VideoConfig
from .positioning import PositioningConfig
from .api_keys import APIKeysConfig
from .accounts import AccountsConfig


class Settings(BaseConfig):
    """Main settings class that combines all configurations"""
    
    def __init__(self):
        # Environment
        self.environment = "development"  # development, production, testing
        self.debug = True
        
        # Application settings
        self.app_name = "Xvion Video Generator"
        self.app_version = "2.0.0"
        
        # Directories
        self.assets_dir = "./assets"
        self.output_dir = "./output"
        self.temp_dir = "./temp"
        self.logs_dir = "./logs"
        self.uploads_dir = "./uploads"
        
        # File settings
        self.photos_dir = "photos"
        self.max_file_size = 100 * 1024 * 1024  # 100MB
        self.allowed_video_formats = ['.mp4', '.avi', '.mov', '.mkv']
        self.allowed_image_formats = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
        self.allowed_audio_formats = ['.mp3', '.wav', '.aac', '.m4a']
        
        # Processing settings
        self.max_concurrent_jobs = 3
        self.job_timeout_seconds = 3600  # 1 hour
        self.cleanup_temp_files = True
        self.preserve_logs_days = 30
        
        # Arabic support
        self.arabic_support = True
        self.arabic_letters = ['أ', 'ب', 'ج']  # Arabic letters for A, B, C
        
        # Fallback photos
        self.fallback_photos = [
            "https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=800&h=450&fit=crop",
            "https://images.unsplash.com/photo-1434030216411-0b793f4b4173?w=800&h=450&fit=crop",
            "https://images.unsplash.com/photo-*************-21bda4d32df4?w=800&h=450&fit=crop",
            "https://images.unsplash.com/photo-*************-c6227db76b6e?w=800&h=450&fit=crop",
            "https://images.unsplash.com/photo-*************-21bda4d32df4?w=800&h=450&fit=crop",
        ]
        
        # Initialize sub-configurations
        self.video: Optional[VideoConfig] = None
        self.positioning: Optional[PositioningConfig] = None
        self.api_keys: Optional[APIKeysConfig] = None
        self.accounts: Optional[AccountsConfig] = None
        
        super().__init__()
        
        # Initialize sub-configurations after base initialization
        self._initialize_sub_configs()
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        self.environment = self.get_env_var('ENVIRONMENT', self.environment)
        self.debug = self.get_env_bool('DEBUG', self.debug)
        
        self.app_name = self.get_env_var('APP_NAME', self.app_name)
        self.app_version = self.get_env_var('APP_VERSION', self.app_version)
        
        # Directories
        self.assets_dir = self.get_env_var('ASSETS_DIR', self.assets_dir)
        self.output_dir = self.get_env_var('OUTPUT_DIR', self.output_dir)
        self.temp_dir = self.get_env_var('TEMP_DIR', self.temp_dir)
        self.logs_dir = self.get_env_var('LOGS_DIR', self.logs_dir)
        self.uploads_dir = self.get_env_var('UPLOADS_DIR', self.uploads_dir)
        
        # File settings
        self.photos_dir = self.get_env_var('PHOTOS_DIR', self.photos_dir)
        self.max_file_size = self.get_env_int('MAX_FILE_SIZE', self.max_file_size)
        
        # Processing settings
        self.max_concurrent_jobs = self.get_env_int('MAX_CONCURRENT_JOBS', self.max_concurrent_jobs)
        self.job_timeout_seconds = self.get_env_int('JOB_TIMEOUT_SECONDS', self.job_timeout_seconds)
        self.cleanup_temp_files = self.get_env_bool('CLEANUP_TEMP_FILES', self.cleanup_temp_files)
        self.preserve_logs_days = self.get_env_int('PRESERVE_LOGS_DAYS', self.preserve_logs_days)
        
        # Arabic support
        self.arabic_support = self.get_env_bool('ARABIC_SUPPORT', self.arabic_support)
        
        # Load fallback photos from environment
        fallback_photos_env = self.get_env_var('FALLBACK_PHOTOS')
        if fallback_photos_env:
            self.fallback_photos = [url.strip() for url in fallback_photos_env.split(',') if url.strip()]
    
    def _validate_config(self) -> None:
        """Validate configuration values"""
        # Validate environment
        valid_environments = ['development', 'production', 'testing']
        if self.environment not in valid_environments:
            raise ValueError(f"Environment must be one of: {valid_environments}")
        
        # Validate directories exist or can be created
        directories = [self.assets_dir, self.output_dir, self.temp_dir, self.logs_dir, self.uploads_dir]
        for directory in directories:
            if not os.path.exists(directory):
                try:
                    os.makedirs(directory, exist_ok=True)
                except OSError as e:
                    print(f"Warning: Could not create directory {directory}: {e}")
        
        # Validate processing settings
        if self.max_concurrent_jobs <= 0:
            raise ValueError("Max concurrent jobs must be positive")
        
        if self.job_timeout_seconds <= 0:
            raise ValueError("Job timeout must be positive")
        
        if self.max_file_size <= 0:
            raise ValueError("Max file size must be positive")
        
        if self.preserve_logs_days < 0:
            raise ValueError("Preserve logs days must be non-negative")
    
    def _initialize_sub_configs(self) -> None:
        """Initialize sub-configuration objects"""
        self.video = VideoConfig()
        self.positioning = PositioningConfig()
        self.api_keys = APIKeysConfig()
        self.accounts = AccountsConfig()
    
    @property
    def is_production(self) -> bool:
        """Check if running in production environment"""
        return self.environment == 'production'
    
    @property
    def is_development(self) -> bool:
        """Check if running in development environment"""
        return self.environment == 'development'
    
    @property
    def is_testing(self) -> bool:
        """Check if running in testing environment"""
        return self.environment == 'testing'
    
    def get_temp_file_path(self, filename: str) -> str:
        """Get full path for temporary file"""
        return os.path.join(self.temp_dir, filename)
    
    def get_output_file_path(self, filename: str) -> str:
        """Get full path for output file"""
        return os.path.join(self.output_dir, filename)
    
    def get_asset_file_path(self, filename: str) -> str:
        """Get full path for asset file"""
        return os.path.join(self.assets_dir, filename)
    
    def get_log_file_path(self, filename: str) -> str:
        """Get full path for log file"""
        return os.path.join(self.logs_dir, filename)
    
    def get_upload_file_path(self, filename: str) -> str:
        """Get full path for uploaded file"""
        return os.path.join(self.uploads_dir, filename)
    
    def cleanup_old_logs(self) -> None:
        """Clean up old log files based on preserve_logs_days setting"""
        if self.preserve_logs_days <= 0:
            return
        
        import time
        import glob
        
        cutoff_time = time.time() - (self.preserve_logs_days * 24 * 60 * 60)
        log_pattern = os.path.join(self.logs_dir, "*.log")
        
        for log_file in glob.glob(log_pattern):
            try:
                if os.path.getmtime(log_file) < cutoff_time:
                    os.remove(log_file)
                    print(f"Removed old log file: {log_file}")
            except OSError as e:
                print(f"Error removing log file {log_file}: {e}")
    
    def to_dict(self, include_sub_configs: bool = True) -> dict:
        """Convert to dictionary"""
        data = super().to_dict()
        
        if include_sub_configs:
            data['video'] = self.video.to_dict() if self.video else None
            data['positioning'] = self.positioning.to_dict() if self.positioning else None
            data['api_keys'] = self.api_keys.to_dict() if self.api_keys else None
            data['accounts'] = self.accounts.to_dict() if self.accounts else None
        
        return data


# Global settings instance
settings = Settings()
