"""
Positioning and layout configuration
"""

from typing import Dict, Any, List, Tuple
from .base import BaseConfig


class PositioningConfig(BaseConfig):
    """Positioning and layout configuration"""
    
    def __init__(self):
        # Account name text box
        self.account_name_text_box = {
            "width": 1150,
            "height": 120,
            "x": -25,
            "y": 160,
            "corner_radius": 20,
            "border_width": 0,
            "text_color": (255, 255, 255),
            "shadow_color": (0, 0, 0, 100),
            "shadow_offset": (2, 2),
            "font_size": 75,
            "gradient_start": (0, 0, 0),
            "gradient_end": (0, 0, 0),
        }
        
        # Photo configuration
        self.photo_config = {
            "width": 250,
            "height": 250,
            "spacing": 30,
            "total_width": 840,
            "x_start": 140,
            "y": 450,
        }
        
        # Emoji configuration
        self.emoji_config = {
            "size": 120,
            "x_offset": -20,
            "y_base": 860,
            "spacing": 180,
        }
        
        # Question text box configuration
        self.question_text_box = {
            "width": 920,
            "height": 530,
            "x": 90,
            "y": 235,
            "corner_radius": 50,
            "border_width": 10,
            "fill_color": (255, 255, 255),
            "shadow_color": (0, 0, 0, 150),
            "shadow_offset": (2, 2),
        }
        
        # Options text box configuration
        self.text_box_config = {
            "width": 850,
            "height": 170,
            "x": 140,
            "y_base": 770,
            "spacing": 200,
            "corner_radius": 30,
            "border_width": 7,
            "fill_color": (255, 255, 255),
            "text_color": (0, 0, 0),
            "shadow_color": (0, 0, 0, 180),
            "shadow_offset": (3, 3),
            "letter_circle_radius": 80,
            "letter_circle_color": (138, 43, 226),
            "letter_text_color": (255, 255, 255),
            "letter_offset_x": -370,
            "correct_fill_color": (34, 197, 94),
            "correct_border_color": (22, 163, 74),
            "correct_letter_circle_color": (21, 128, 61),
            "option_text_size": 80,
            "letter_text_size": 85,
            "letter_x_offset": 0,
            "letter_y_offset": -12,
            "text_x_offset": 60,
            "text_y_offset": -20,
        }
        
        # Letter circle stroke configuration
        self.letter_circle_stroke = {
            "enabled": True,
            "color": "white",  # "white", "same_as_border", "random"
            "width": 5
        }
        
        # Color modes
        self.color_modes = {
            "mode1": {
                "question_text_box": {
                    "fill_color": (255, 255, 255),
                    "text_color": (0, 0, 0),
                    "shadow_color": (0, 0, 0, 150),
                },
                "options_text_box": {
                    "fill_color": (255, 255, 255),
                    "text_color": (0, 0, 0),
                    "border_color": (0, 0, 0),
                    "shadow_color": (0, 0, 0, 180),
                }
            },
            "mode2": {
                "question_text_box": {
                    "fill_color": (0, 0, 0),
                    "text_color": (255, 255, 255),
                    "shadow_color": (255, 255, 255, 150),
                },
                "options_text_box": {
                    "fill_color": (0, 0, 0),
                    "text_color": (255, 255, 255),
                    "border_color": (255, 255, 255),
                    "shadow_color": (255, 255, 255, 180),
                }
            },
            "mode3": {
                "question_text_box": {
                    "fill_color": (255, 255, 255),
                    "text_color": (0, 0, 0),
                    "shadow_color": (0, 0, 0, 150),
                },
                "options_text_box": {
                    "fill_color": (0, 0, 0),
                    "text_color": (255, 255, 255),
                    "border_color": (255, 255, 255),
                    "shadow_color": (255, 255, 255, 180),
                }
            },
            "mode4": {
                "question_text_box": {
                    "fill_color": (0, 0, 0),
                    "text_color": (255, 255, 255),
                    "shadow_color": (255, 255, 255, 150),
                },
                "options_text_box": {
                    "fill_color": (255, 255, 255),
                    "text_color": (0, 0, 0),
                    "border_color": (0, 0, 0),
                    "shadow_color": (0, 0, 0, 180),
                }
            }
        }
        
        # Follow me configuration
        self.follow_me_config = {
            "text": "شارك اجابتك في التعليقات",
            "fontsize": 90,
            "fontcolor": "red",
            "x": 520,
            "y": 1570,
            "flicker_speed": 6,
        }
        
        # Position configuration for legacy compatibility
        self.position_config = {
            "question": {"x": -435, "y": 800, "line_spacing": 100},
            "question_part2": {"x": -435, "y": 760},
            "options": {"x": 160, "y_base": 850, "option_spacing": 160},
        }
        
        super().__init__()
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        # Allow environment overrides for key positioning values
        self.photo_config["width"] = self.get_env_int('PHOTO_WIDTH', self.photo_config["width"])
        self.photo_config["height"] = self.get_env_int('PHOTO_HEIGHT', self.photo_config["height"])
        self.emoji_config["size"] = self.get_env_int('EMOJI_SIZE', self.emoji_config["size"])
        
        # Text box dimensions
        self.text_box_config["width"] = self.get_env_int('TEXT_BOX_WIDTH', self.text_box_config["width"])
        self.text_box_config["height"] = self.get_env_int('TEXT_BOX_HEIGHT', self.text_box_config["height"])
        
        # Font sizes
        self.text_box_config["option_text_size"] = self.get_env_int('OPTION_TEXT_SIZE', self.text_box_config["option_text_size"])
        self.text_box_config["letter_text_size"] = self.get_env_int('LETTER_TEXT_SIZE', self.text_box_config["letter_text_size"])
        
        # Letter circle settings
        self.letter_circle_stroke["enabled"] = self.get_env_bool('LETTER_CIRCLE_STROKE_ENABLED', self.letter_circle_stroke["enabled"])
        self.letter_circle_stroke["width"] = self.get_env_int('LETTER_CIRCLE_STROKE_WIDTH', self.letter_circle_stroke["width"])
    
    def _validate_config(self) -> None:
        """Validate configuration values"""
        # Validate photo dimensions
        if self.photo_config["width"] <= 0 or self.photo_config["height"] <= 0:
            raise ValueError("Photo dimensions must be positive")
        
        # Validate emoji size
        if self.emoji_config["size"] <= 0:
            raise ValueError("Emoji size must be positive")
        
        # Validate text box dimensions
        if self.text_box_config["width"] <= 0 or self.text_box_config["height"] <= 0:
            raise ValueError("Text box dimensions must be positive")
        
        # Validate font sizes
        if self.text_box_config["option_text_size"] <= 0:
            raise ValueError("Option text size must be positive")
        
        if self.text_box_config["letter_text_size"] <= 0:
            raise ValueError("Letter text size must be positive")
        
        # Validate color modes
        required_modes = ["mode1", "mode2", "mode3", "mode4"]
        for mode in required_modes:
            if mode not in self.color_modes:
                raise ValueError(f"Missing color mode: {mode}")
    
    def get_color_mode_config(self, mode: str) -> Dict[str, Any]:
        """Get configuration for specific color mode"""
        if mode not in self.color_modes:
            mode = "mode1"  # Default fallback
        return self.color_modes[mode]
    
    def get_photo_positions(self, num_photos: int = 3) -> List[Tuple[int, int]]:
        """Get positions for multiple photos"""
        positions = []
        for i in range(num_photos):
            x = self.photo_config["x_start"] + i * (self.photo_config["width"] + self.photo_config["spacing"])
            y = self.photo_config["y"]
            positions.append((x, y))
        return positions
    
    def get_emoji_positions(self, num_emojis: int = 3) -> List[Tuple[int, int]]:
        """Get positions for multiple emojis"""
        positions = []
        for i in range(num_emojis):
            x = self.emoji_config["x_offset"]
            y = self.emoji_config["y_base"] + i * self.emoji_config["spacing"]
            positions.append((x, y))
        return positions
    
    def get_text_box_positions(self, num_options: int = 3) -> List[Tuple[int, int]]:
        """Get positions for option text boxes"""
        positions = []
        for i in range(num_options):
            x = self.text_box_config["x"]
            y = self.text_box_config["y_base"] + i * self.text_box_config["spacing"]
            positions.append((x, y))
        return positions
