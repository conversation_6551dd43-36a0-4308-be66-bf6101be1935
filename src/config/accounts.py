"""
Accounts configuration
"""

from typing import List, Dict, Any, Optional
from .base import BaseConfig
from ..models.account import AccountConfig


class AccountsConfig(BaseConfig):
    """Accounts configuration"""
    
    def __init__(self):
        # Default accounts configuration
        self.accounts: List[AccountConfig] = []
        self.default_account_name = "Account1"
        self.number_of_videos = 1
        
        # Default paths
        self.default_csv_file = "./assets/C_Arabic 3.csv"
        self.default_text_box = "./assets/T1.png"
        self.default_background_folder = "./assets/Back Videos"
        self.default_output_folder = "./output"
        self.default_font_path = "./assets/Fonts/NotoSansArabic-Bold.ttf"
        
        super().__init__()
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        self.number_of_videos = self.get_env_int('NUMBER_OF_VIDEOS', self.number_of_videos)
        self.default_account_name = self.get_env_var('DEFAULT_ACCOUNT_NAME', self.default_account_name)
        
        # Default paths
        self.default_csv_file = self.get_env_var('DEFAULT_CSV_FILE', self.default_csv_file)
        self.default_text_box = self.get_env_var('DEFAULT_TEXT_BOX', self.default_text_box)
        self.default_background_folder = self.get_env_var('DEFAULT_BACKGROUND_FOLDER', self.default_background_folder)
        self.default_output_folder = self.get_env_var('DEFAULT_OUTPUT_FOLDER', self.default_output_folder)
        self.default_font_path = self.get_env_var('DEFAULT_FONT_PATH', self.default_font_path)
        
        # Load accounts from environment or create defaults
        self._load_accounts_from_environment()
    
    def _load_accounts_from_environment(self) -> None:
        """Load accounts configuration from environment variables"""
        # Try to load accounts from environment variable (JSON format)
        accounts_json = self.get_env_var('ACCOUNTS_CONFIG')
        if accounts_json:
            try:
                import json
                accounts_data = json.loads(accounts_json)
                self.accounts = [AccountConfig.from_dict(account) for account in accounts_data]
                return
            except (json.JSONDecodeError, KeyError, TypeError) as e:
                print(f"Warning: Failed to load accounts from environment: {e}")
        
        # Create default accounts if none loaded
        self._create_default_accounts()
    
    def _create_default_accounts(self) -> None:
        """Create default accounts configuration"""
        default_accounts = [
            {
                "name": "Account1",
                "display_name": "@AGL LA3BAH",
                "color_mode": "mode1",
                "csv_file": self.default_csv_file,
                "text_box_path": self.default_text_box,
                "background_folder": self.default_background_folder,
                "output_folder": self.default_output_folder,
                "photo_x": 1,
                "photo_y": 1,
                "photo_width": 1,
                "photo_height": 1,
                "font_path": self.default_font_path
            },
            {
                "name": "Account2",
                "display_name": "@AGL LA3BAH",
                "color_mode": "mode2",
                "csv_file": self.default_csv_file,
                "text_box_path": self.default_text_box,
                "background_folder": self.default_background_folder,
                "output_folder": self.default_output_folder,
                "photo_x": 1,
                "photo_y": 1,
                "photo_width": 1,
                "photo_height": 1,
                "font_path": self.default_font_path
            },
            {
                "name": "Account3",
                "display_name": "@AGL LA3BAH",
                "color_mode": "mode3",
                "csv_file": self.default_csv_file,
                "text_box_path": self.default_text_box,
                "background_folder": self.default_background_folder,
                "output_folder": self.default_output_folder,
                "photo_x": 1,
                "photo_y": 1,
                "photo_width": 1,
                "photo_height": 1,
                "font_path": self.default_font_path
            },
            {
                "name": "Account4",
                "display_name": "@AGL LA3BAH",
                "color_mode": "mode4",
                "csv_file": self.default_csv_file,
                "text_box_path": self.default_text_box,
                "background_folder": self.default_background_folder,
                "output_folder": self.default_output_folder,
                "photo_x": 1,
                "photo_y": 1,
                "photo_width": 1,
                "photo_height": 1,
                "font_path": self.default_font_path
            }
        ]
        
        self.accounts = [AccountConfig.from_dict(account) for account in default_accounts]
    
    def _validate_config(self) -> None:
        """Validate configuration values"""
        if self.number_of_videos <= 0:
            raise ValueError("Number of videos must be positive")
        
        if not self.accounts:
            raise ValueError("At least one account must be configured")
        
        # Validate each account
        for account in self.accounts:
            if not account.name:
                raise ValueError("Account name is required")
            if not account.display_name:
                raise ValueError("Account display name is required")
        
        # Check for duplicate account names
        account_names = [account.name for account in self.accounts]
        if len(account_names) != len(set(account_names)):
            raise ValueError("Account names must be unique")
    
    def get_account_by_name(self, name: str) -> Optional[AccountConfig]:
        """Get account configuration by name"""
        for account in self.accounts:
            if account.name == name:
                return account
        return None
    
    def get_default_account(self) -> Optional[AccountConfig]:
        """Get the default account configuration"""
        return self.get_account_by_name(self.default_account_name)
    
    def add_account(self, account: AccountConfig) -> None:
        """Add a new account configuration"""
        # Check for duplicate names
        if self.get_account_by_name(account.name):
            raise ValueError(f"Account with name '{account.name}' already exists")
        
        self.accounts.append(account)
    
    def remove_account(self, name: str) -> bool:
        """Remove account configuration by name"""
        for i, account in enumerate(self.accounts):
            if account.name == name:
                del self.accounts[i]
                return True
        return False
    
    def update_account(self, name: str, updates: Dict[str, Any]) -> bool:
        """Update account configuration"""
        account = self.get_account_by_name(name)
        if not account:
            return False
        
        for key, value in updates.items():
            if hasattr(account, key):
                setattr(account, key, value)
        
        return True
    
    @property
    def account_names(self) -> List[str]:
        """Get list of all account names"""
        return [account.name for account in self.accounts]
    
    @property
    def account_count(self) -> int:
        """Get number of configured accounts"""
        return len(self.accounts)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary"""
        data = super().to_dict()
        data['accounts'] = [account.to_dict() for account in self.accounts]
        return data
    
    def export_accounts_json(self) -> str:
        """Export accounts configuration as JSON string"""
        import json
        accounts_data = [account.to_dict() for account in self.accounts]
        return json.dumps(accounts_data, indent=2)
    
    def import_accounts_json(self, json_str: str) -> bool:
        """Import accounts configuration from JSON string"""
        try:
            import json
            accounts_data = json.loads(json_str)
            new_accounts = [AccountConfig.from_dict(account) for account in accounts_data]
            
            # Validate new accounts
            for account in new_accounts:
                if not account.name or not account.display_name:
                    raise ValueError("Invalid account configuration")
            
            self.accounts = new_accounts
            return True
        except (json.JSONDecodeError, KeyError, TypeError, ValueError) as e:
            print(f"Error importing accounts: {e}")
            return False
