"""
Video generation configuration
"""

from typing import Dict, Any
from .base import BaseConfig


class VideoConfig(BaseConfig):
    """Video generation configuration"""
    
    def __init__(self):
        # Default values
        self.width = 1920
        self.height = 1080
        self.fps = 30
        self.bitrate = "5000k"
        self.codec = "libx264"
        self.format = "mp4"
        
        # Timing settings
        self.question_duration = 11.0  # seconds per question
        self.answer_display_time = 2.5  # seconds to show answer
        self.last_answer_time = 8.5  # when answer appears
        self.green_reveal_time = 8.0  # when to show green highlight
        
        # Quality settings
        self.video_quality = "high"  # low, medium, high
        self.audio_quality = "high"  # low, medium, high
        
        # Feature flags
        self.enable_photos = True
        self.enable_emojis = True
        self.enable_audio = True
        self.enable_subtitles = False
        
        # Processing settings
        self.max_concurrent_jobs = 3
        self.cleanup_temp_files = True
        self.preserve_intermediate_files = False
        
        super().__init__()
    
    def _load_from_environment(self) -> None:
        """Load configuration from environment variables"""
        self.width = self.get_env_int('VIDEO_WIDTH', self.width)
        self.height = self.get_env_int('VIDEO_HEIGHT', self.height)
        self.fps = self.get_env_int('VIDEO_FPS', self.fps)
        self.bitrate = self.get_env_var('VIDEO_BITRATE', self.bitrate)
        self.codec = self.get_env_var('VIDEO_CODEC', self.codec)
        self.format = self.get_env_var('VIDEO_FORMAT', self.format)
        
        self.question_duration = self.get_env_float('QUESTION_DURATION', self.question_duration)
        self.answer_display_time = self.get_env_float('ANSWER_DISPLAY_TIME', self.answer_display_time)
        self.last_answer_time = self.get_env_float('LAST_ANSWER_TIME', self.last_answer_time)
        self.green_reveal_time = self.get_env_float('GREEN_REVEAL_TIME', self.green_reveal_time)
        
        self.video_quality = self.get_env_var('VIDEO_QUALITY', self.video_quality)
        self.audio_quality = self.get_env_var('AUDIO_QUALITY', self.audio_quality)
        
        self.enable_photos = self.get_env_bool('ENABLE_PHOTOS', self.enable_photos)
        self.enable_emojis = self.get_env_bool('ENABLE_EMOJIS', self.enable_emojis)
        self.enable_audio = self.get_env_bool('ENABLE_AUDIO', self.enable_audio)
        self.enable_subtitles = self.get_env_bool('ENABLE_SUBTITLES', self.enable_subtitles)
        
        self.max_concurrent_jobs = self.get_env_int('MAX_CONCURRENT_JOBS', self.max_concurrent_jobs)
        self.cleanup_temp_files = self.get_env_bool('CLEANUP_TEMP_FILES', self.cleanup_temp_files)
        self.preserve_intermediate_files = self.get_env_bool('PRESERVE_INTERMEDIATE_FILES', self.preserve_intermediate_files)
    
    def _validate_config(self) -> None:
        """Validate configuration values"""
        if self.width <= 0 or self.height <= 0:
            raise ValueError("Video dimensions must be positive")
        
        if self.fps <= 0:
            raise ValueError("FPS must be positive")
        
        if self.question_duration <= 0:
            raise ValueError("Question duration must be positive")
        
        if self.answer_display_time <= 0:
            raise ValueError("Answer display time must be positive")
        
        if self.last_answer_time >= self.question_duration:
            raise ValueError("Last answer time must be less than question duration")
        
        if self.green_reveal_time >= self.question_duration:
            raise ValueError("Green reveal time must be less than question duration")
        
        valid_qualities = ['low', 'medium', 'high']
        if self.video_quality not in valid_qualities:
            raise ValueError(f"Video quality must be one of: {valid_qualities}")
        
        if self.audio_quality not in valid_qualities:
            raise ValueError(f"Audio quality must be one of: {valid_qualities}")
        
        if self.max_concurrent_jobs <= 0:
            raise ValueError("Max concurrent jobs must be positive")
    
    @property
    def resolution_string(self) -> str:
        """Get resolution as string (e.g., '1920x1080')"""
        return f"{self.width}x{self.height}"
    
    def get_quality_settings(self) -> Dict[str, Any]:
        """Get quality-specific settings"""
        quality_map = {
            'low': {
                'bitrate': '2000k',
                'preset': 'fast',
                'crf': '28'
            },
            'medium': {
                'bitrate': '3500k',
                'preset': 'medium',
                'crf': '23'
            },
            'high': {
                'bitrate': '5000k',
                'preset': 'slow',
                'crf': '18'
            }
        }
        return quality_map.get(self.video_quality, quality_map['high'])
    
    def get_ffmpeg_video_args(self) -> list:
        """Get FFmpeg arguments for video encoding"""
        quality_settings = self.get_quality_settings()
        
        args = [
            '-c:v', self.codec,
            '-b:v', quality_settings['bitrate'],
            '-preset', quality_settings['preset'],
            '-crf', quality_settings['crf'],
            '-r', str(self.fps),
            '-s', self.resolution_string
        ]
        
        return args
    
    def get_total_duration(self, num_questions: int) -> float:
        """Calculate total video duration for given number of questions"""
        return num_questions * self.question_duration
