# Xvion Video Generator V2.0.0 - Clean Architecture

This document describes the new Object-Oriented Programming (OOP) structure for the Xvion Video Generator, refactored from the original monolithic design to follow clean architecture principles.

## 🏗️ Architecture Overview

The new structure follows **Clean Architecture** principles with clear separation of concerns:

- **Domain Models** (`src/models/`) - Core business entities and data structures
- **Services** (`src/services/`) - Business logic and orchestration
- **Configuration** (`src/config/`) - Settings and configuration management
- **Interfaces** (`src/interfaces/`) - Abstract contracts for services
- **Utilities** (`src/utils/`) - Helper functions and common operations
- **Exceptions** (`src/exceptions/`) - Custom error handling

## 📁 Project Structure

```
src/
├── models/                 # 🏛️ Domain Models
│   ├── __init__.py
│   ├── quiz.py            # Quiz and Question entities
│   ├── video.py           # Video generation models
│   ├── account.py         # Account configuration models
│   ├── media.py           # Media asset models (Photo, Audio, Emoji)
│   └── positioning.py     # Layout and positioning models
│
├── services/              # ⚙️ Business Logic Services
│   ├── __init__.py
│   ├── video_generation_service.py    # Main video generation orchestrator
│   ├── translation_service.py         # DeepL translation integration
│   ├── audio_service.py              # ElevenLabs audio generation
│   ├── photo_service.py              # Multi-API photo search
│   ├── emoji_service.py              # Emoji processing
│   ├── text_processing_service.py    # Arabic text processing
│   └── media_collection_service.py   # Media asset collection
│
├── config/                # ⚙️ Configuration Management
│   ├── __init__.py
│   ├── base.py           # Abstract base configuration
│   ├── video.py          # Video generation settings
│   ├── positioning.py    # Layout and positioning settings
│   ├── api_keys.py       # API keys and authentication
│   ├── accounts.py       # Account configurations
│   └── settings.py       # Main settings aggregator
│
├── exceptions/            # ❌ Custom Exception Handling
│   ├── __init__.py
│   ├── base.py           # Base exception classes
│   ├── video.py          # Video generation exceptions
│   ├── media.py          # Media processing exceptions
│   ├── translation.py    # Translation service exceptions
│   └── quiz.py           # Quiz data exceptions
│
├── interfaces/            # 📋 Abstract Interfaces
│   ├── __init__.py
│   ├── video_generator.py      # Video generation contract
│   ├── translation_service.py  # Translation service contract
│   ├── media_service.py        # Media services contracts
│   ├── text_processor.py       # Text processing contract
│   └── repository.py           # Data access contracts
│
├── utils/                 # 🛠️ Utility Classes
│   ├── __init__.py
│   ├── file_utils.py     # File system operations
│   ├── video_utils.py    # Video processing utilities
│   ├── image_utils.py    # Image manipulation utilities
│   ├── text_utils.py     # Text processing utilities
│   ├── csv_utils.py      # CSV file handling
│   └── color_utils.py    # Color manipulation utilities
│
├── core/                  # 🎯 Core Processing Modules
│   ├── video/            # Video processing components
│   ├── text/             # Text rendering and processing
│   └── image/            # Image processing components
│
├── repositories/          # 💾 Data Access Layer
│   ├── __init__.py
│   ├── quiz_repository.py      # Quiz data access
│   ├── video_job_repository.py # Video job persistence
│   └── media_repository.py     # Media asset storage
│
└── main.py               # 🚀 Main application entry point
```

## 🎯 Key Features

### 1. **Domain Models** (`src/models/`)
- **Quiz & Question**: Core quiz entities with validation
- **VideoJob**: Video generation job tracking with status management
- **Account**: User account and configuration models
- **Media**: Photo, Audio, Emoji asset models with metadata
- **Positioning**: Layout and positioning configuration

### 2. **Service Layer** (`src/services/`)
- **VideoGenerationService**: Main orchestrator for video creation
- **TranslationService**: DeepL API integration for multi-language support
- **AudioService**: ElevenLabs TTS integration
- **PhotoService**: Multi-API photo search (Unsplash, Pixabay, Pexels)
- **EmojiService**: Emoji processing and caching

### 3. **Configuration Management** (`src/config/`)
- Environment variable support
- Type-safe configuration classes
- API key management with security features
- Centralized settings with validation

### 4. **Exception Handling** (`src/exceptions/`)
- Structured error hierarchy
- Context-aware error messages
- Detailed error information for debugging
- Service-specific exception types

## 🚀 Getting Started

### 1. Installation

```bash
# Install dependencies
pip install -r requirements.txt

# Set up environment variables
cp .env.example .env
# Edit .env with your API keys
```

### 2. Configuration

Create your configuration in `.env`:

```env
# API Keys
DEEPL_AUTH_KEY=your_deepl_key
ELEVENLABS_API_KEY=your_elevenlabs_key
UNSPLASH_ACCESS_KEY=your_unsplash_key
PIXABAY_API_KEY=your_pixabay_key
PEXELS_API_KEY=your_pexels_key

# Paths
BACKGROUND_VIDEOS_PATH=./assets/Back Videos
OUTPUT_PATH=./output
FONTS_PATH=./assets/Fonts
TEMP_PATH=./temp

# Video Settings
DEFAULT_VIDEO_QUALITY=high
DEFAULT_COLOR_MODE=mode1
CLEANUP_TEMP_FILES=true
```

### 3. Basic Usage

```python
import asyncio
from src.services.video_generation_service import VideoGenerationService
from src.models.quiz import Quiz, Question
from src.models.video import VideoGenerationConfig
from src.models.account import AccountConfig

async def generate_video():
    # Initialize service
    video_service = VideoGenerationService()
    
    # Create quiz
    quiz = Quiz(id="test", title="Test Quiz", language="ar")
    # Add questions...
    
    # Configure generation
    config = VideoGenerationConfig(
        account_name="TestAccount",
        num_questions=6,
        enable_photos=True,
        enable_audio=True
    )
    
    # Generate video
    result = await video_service.generate_video_from_quiz(
        quiz=quiz,
        config=config,
        account_config=account_config,
        output_path="./output/video.mp4"
    )
    
    print(f"Video generated: {result}")

# Run
asyncio.run(generate_video())
```

### 4. Running the Demo

```bash
# Run the demonstration
python src/main.py
```

## 🔧 Development

### Adding New Services

1. Create interface in `src/interfaces/`
2. Implement service in `src/services/`
3. Add configuration in `src/config/`
4. Create custom exceptions in `src/exceptions/`
5. Add utilities in `src/utils/` if needed

### Testing

```bash
# Run tests (when implemented)
python -m pytest tests/

# Run specific test
python -m pytest tests/test_video_generation.py
```

## 📊 Benefits of New Structure

### ✅ **Maintainability**
- Clear separation of concerns
- Single responsibility principle
- Easy to locate and modify code

### ✅ **Testability**
- Dependency injection ready
- Mockable interfaces
- Isolated components

### ✅ **Scalability**
- Easy to add new features
- Modular architecture
- Plugin-ready design

### ✅ **Reliability**
- Comprehensive error handling
- Type safety with dataclasses
- Configuration validation

### ✅ **Developer Experience**
- Clear code organization
- Self-documenting interfaces
- Consistent patterns

## 🔄 Migration from Legacy Code

The original `Video Generator V2.0.0.py` (7000+ lines) has been refactored into:

- **50+ focused classes** with single responsibilities
- **Type-safe models** with validation
- **Service-oriented architecture** with clear interfaces
- **Comprehensive error handling** with context
- **Configuration management** with environment support

## 🤝 Contributing

1. Follow the established architecture patterns
2. Add appropriate tests for new features
3. Update documentation for API changes
4. Use type hints and dataclasses
5. Handle errors with custom exceptions

## 📝 License

This project maintains the same license as the original Xvion Video Generator.

---

**Note**: This new structure is designed to be production-ready while maintaining all the functionality of the original monolithic version. The refactoring improves code quality, maintainability, and developer experience significantly.
